{"info": {"author": "z9r", "authorUrl": "", "name": "Welcome to Night City 2.21", "description": "", "installInstructions": "[If you have any questions about the collection, need assistance, or just want to let us know what you thought please click here to join our Discord](https://discord.gg/eJdMQKnQVt)\n\n**Important Decision Near End of Install**\n\nYou will choose your experience for the collection between **Welcome to Night City** as our default or **Cyberpunk THING** for those wanting to trek into more dangerous waters\n\nOnce you make your choice, wait for it to deploy and you will get a popup stating the collection install is done\n\n**Welcome to Night City**\n\nVanilla plus, vanilla balance. Lots of fun mods to add new features which are natively integrated into the game. The HUD will be very familiar to you. The world isn't necessarily out to get you in this... you're a badass \n\n**Cyberpunk THING**\n\nTired of the day-to-day in WTNC? Looking for more of a challenge? Cyberpunk THING offers a world more focused on immersion and realism, featuring a reduced (but sensible) HUD, more difficult and varied enemies, and more balanced cyberware options, just to name a few. A bullet's a bullet, and Night City is full of hardened gangoons with hair triggers and bad attitudes. But they've never run into anything like you.", "domainName": "cyberpunk2077", "gameVersions": ["*******"]}, "mods": [{"name": "input_loader.zip", "version": "0.2.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4575, "fileId": 70374, "md5": "dbcc1271947edec963464cc17d0d80c9", "fileSize": 411954, "logicalFilename": "input_loader.zip", "updatePolicy": "exact", "tag": "E-8Yf8yOP"}, "author": "<PERSON>", "details": {"category": "Utilities", "type": ""}, "phase": 0}, {"name": "TweakXL", "version": "1.10.10", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4197, "fileId": 108558, "md5": "47526e5797d70304ae9c8e8eac1830dd", "fileSize": 1027097, "logicalFilename": "TweakXL", "updatePolicy": "exact", "tag": "qkLc0R28tO"}, "hashes": [{"path": "red4ext\\plugins\\TweakXL\\LICENSE", "md5": "b2ac56eabdd1e9306010d9e8552faff9"}, {"path": "red4ext\\plugins\\TweakXL\\THIRD_PARTY_LICENSES", "md5": "ea7b20872bce32247312e969416328d5"}, {"path": "red4ext\\plugins\\TweakXL\\TweakXL.dll", "md5": "6db6937406312dec065415c8e51eb2d0"}, {"path": "red4ext\\plugins\\TweakXL\\Data\\ExtraFlats.dat", "md5": "1486f0bf47fc31ec7fa0a7f069df7f1b"}, {"path": "red4ext\\plugins\\TweakXL\\Data\\InheritanceMap.dat", "md5": "80940e22590306c3ad8cef7c6446f2e3"}, {"path": "red4ext\\plugins\\TweakXL\\Scripts\\TweakXL.Global.reds", "md5": "6d51394a9341861db298a34336afcbef"}, {"path": "red4ext\\plugins\\TweakXL\\Scripts\\TweakXL.reds", "md5": "d8a73f0ab4ea3fa211f95a9be12bba3b"}], "author": "psiberx", "details": {"category": "Modders Resources", "type": ""}, "phase": 0}, {"name": "mod_settings.zip", "version": "0.2.8", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4885, "fileId": 72402, "md5": "c8dc32fe2093d50a79b2013190aa6db3", "fileSize": 335086, "logicalFilename": "mod_settings.zip", "updatePolicy": "exact", "tag": "tSjAcrerV4"}, "hashes": [{"path": "red4ext\\plugins\\mod_settings\\license.md", "md5": "a58baa42f82472faef093a21c5481d69"}, {"path": "red4ext\\plugins\\mod_settings\\ModSettings.archive", "md5": "d44cee6653ac7b620bfbd6e6f1e6a029"}, {"path": "red4ext\\plugins\\mod_settings\\ModSettings.archive.xl", "md5": "9256da8c94a1dfbd640a610047d9b7d0"}, {"path": "red4ext\\plugins\\mod_settings\\module.reds", "md5": "3c4b48fc6d5ccea6db4cd4b911fdbb99"}, {"path": "red4ext\\plugins\\mod_settings\\mod_settings.dll", "md5": "12537f7f2760ccef714d8da793e32845"}, {"path": "red4ext\\plugins\\mod_settings\\packed.reds", "md5": "63c7403954ddd451df10970afdf5052f"}, {"path": "red4ext\\plugins\\mod_settings\\readme.md", "md5": "69c6893a6c0bd43a24ebb19c63eaea00"}], "author": "<PERSON>", "details": {"category": "User Interface", "type": ""}, "phase": 0}, {"name": "Native Settings UI 1.96", "version": "1.96", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 3518, "fileId": 63684, "md5": "6b4a8689d604cde7f3066fb55e197f48", "fileSize": 15792, "logicalFilename": "Native Settings UI 1.96", "updatePolicy": "exact", "tag": "_OiUvhlO1D"}, "hashes": [{"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\nativeSettings\\Cron.lua", "md5": "172f12af8b5a27b6947a30dc7c0684c6"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\nativeSettings\\EventProxy.lua", "md5": "7dfd6f7224ff90d95f4e838e50047318"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\nativeSettings\\init.lua", "md5": "0b21e4a3a580ee6ec1155ea4d2072a3b"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\nativeSettings\\Ref.lua", "md5": "3c376c279bf299907a113fb639f1d40b"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\nativeSettings\\UIButton.lua", "md5": "e12f944ae605eb8d16d8f21df57b1b5c"}], "author": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Modders Resources", "type": ""}, "phase": 0}, {"name": "BrowserExtensionFramework", "version": "0.9.5", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10038, "fileId": 59169, "md5": "7f82c02f59bd234862cb128b91363b5b", "fileSize": 21897, "logicalFilename": "BrowserExtensionFramework", "updatePolicy": "exact", "tag": "l0vhb__tRN"}, "hashes": [{"path": "r6\\scripts\\BrowserExtension\\browserController.overrides.reds", "md5": "49e646840eb7f48bad5b8839f56e8925"}, {"path": "r6\\scripts\\BrowserExtension\\BrowserExtension.Classes.reds", "md5": "b7c5ba403b7935cbff023915e28bfa1a"}, {"path": "r6\\scripts\\BrowserExtension\\BrowserExtension.DataStructures.reds", "md5": "55bab414c57a3161e031404e5996ed70"}, {"path": "r6\\scripts\\BrowserExtension\\BrowserExtension.System.reds", "md5": "85d64d083a48480c4032607201899373"}, {"path": "r6\\scripts\\BrowserExtension\\HomePagePagination.reds", "md5": "b3d4d0f0572ae3ab9050b36e95bb0cd1"}], "author": "r457 and gh057", "details": {"category": "Modders Resources", "type": ""}, "phase": 0}, {"name": "Deceptious Quest Core", "version": "3.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7831, "fileId": 90374, "md5": "8ba0f1c54aa58a401cba1547ab792b85", "fileSize": 2749150, "logicalFilename": "Deceptious Quest Core", "updatePolicy": "exact", "tag": "FcqO_G2mIj"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 0}, {"name": "Cyber Engine Tweaks - CET 1.35.0 - Patch 2.21", "version": "1.35.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 107, "fileId": 99133, "md5": "0094ebc064f2f75b6699fbd44981812c", "fileSize": 35276081, "logicalFilename": "CET 1.35.0 - Patch 2.21", "updatePolicy": "exact", "tag": "7h83okGPBw"}, "hashes": [{"path": "bin\\x64\\global.ini", "md5": "c044b277b9a24f95739d17868ecd2527"}, {"path": "bin\\x64\\LICENSE", "md5": "33e41ee247da35b99c5b273bf3d8dcdf"}, {"path": "bin\\x64\\version.dll", "md5": "eff276d8edd0f29dada36b0ea1d1f8c8"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks.asi", "md5": "a511211c003ac037021e1dd47f1dc1db"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\ThirdParty_LICENSES", "md5": "f1405b4606283f3035d49106fa202bdf"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\fonts\\materialdesignicons.ttf", "md5": "dd74f11e425603c7adb66100f161b2a5"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\fonts\\NotoSans-Regular.ttf", "md5": "ac08e269b7f479624b266c0ea20013b4"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\fonts\\NotoSansJP-Regular.otf", "md5": "ecfed48e463db4e31d1691c8af367730"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\fonts\\NotoSansKR-Regular.otf", "md5": "210989664066c01d8ffdbdf56bb773cd"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\fonts\\NotoSansMono-Regular.ttf", "md5": "06dfcbf7f29afc8f9fac75040fd18b06"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\fonts\\NotoSansSC-Regular.otf", "md5": "e3ae561f7b8052d9aa9f2b0b09c33ea1"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\fonts\\NotoSansTC-Regular.otf", "md5": "d6b43f6600389d7442f317adfbbd9942"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\fonts\\NotoSansThai-Regular.ttf", "md5": "0a4922cc5fdf576faa5133ea15e45517"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\scripts\\IconGlyphs\\icons.lua", "md5": "c8d80bc8e37c21a4d10e3077555e6645"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\scripts\\json\\json.lua", "md5": "a6f4c77566eafcc87009072f11f16a49"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\scripts\\json\\LICENSE", "md5": "f0ab9756bdc147013bc6b078a8888f14"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\scripts\\json\\README.md", "md5": "74ab493dce25ad4345bdb0e35de3c398"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\tweakdb\\tweakdbstr.kark", "md5": "15f2abea3ab675caa870fb31af40ba28"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\tweakdb\\usedhashes.kark", "md5": "1fd169da7419cf43f00e1f1ebbdd5a23"}], "author": "yamashi", "details": {"category": "Modders Resources", "type": ""}, "phase": 0}, {"name": "RED4ext", "version": "1.27.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 2380, "fileId": 99122, "md5": "3351156dfb358a598600bfa1851a4bc4", "fileSize": 730755, "logicalFilename": "RED4ext", "updatePolicy": "exact", "tag": "VLePWH3wKh"}, "hashes": [{"path": "bin\\x64\\winmm.dll", "md5": "b00b2f6f2704cd0ae9834fd1b7ddaff5"}, {"path": "red4ext\\LICENSE.txt", "md5": "61ce38ba9eaf014edb0991e771218604"}, {"path": "red4ext\\RED4ext.dll", "md5": "f79e42d11aad6ebf8d15a09018446af5"}, {"path": "red4ext\\THIRD_PARTY_LICENSES.txt", "md5": "aad63133ad20aac9d95d89082fa76873"}], "author": "WopsS", "details": {"category": "Modders Resources", "type": ""}, "phase": 0}, {"name": "VendorsXL", "version": "1.0.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19679, "fileId": 104560, "md5": "c2fdfdfbb84117e9dbe1226aa409f5c8", "fileSize": 234525, "logicalFilename": "VendorsXL", "updatePolicy": "exact", "tag": "aZTF9MdBl6"}, "author": "Deceptious", "details": {"category": "Modders Resources", "type": ""}, "phase": 0}, {"name": "redscript", "version": "0.5.28", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 1511, "fileId": 101739, "md5": "a98715c8ac6e2a61a07a428e84b69ab1", "fileSize": 949414, "logicalFilename": "redscript", "updatePolicy": "exact", "tag": "i3dJypRUWr"}, "hashes": [{"path": "engine\\config\\base\\scripts.ini", "md5": "7b8e35dbada0f834b88ee976ca789f76"}, {"path": "engine\\tools\\scc.exe", "md5": "f74270d37daa8b4cacc2a9edf1f2daf1"}, {"path": "engine\\tools\\scc_lib.dll", "md5": "6677fda0f54fa50f612b11329ebc45f4"}, {"path": "r6\\config\\cybercmd\\scc.toml", "md5": "e740952ce5d49f460dca9067536ec375"}], "author": "jac3km4", "details": {"category": "Modders Resources", "type": ""}, "phase": 0}, {"name": "ArchiveXL", "version": "1.23.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4198, "fileId": 109497, "md5": "e4f68f929bc23009aaeb1db8f02bc586", "fileSize": 984647, "logicalFilename": "ArchiveXL", "updatePolicy": "exact", "tag": "RxI2Xo6XEO"}, "hashes": [{"path": "r6\\config\\redsUserHints\\ArchiveXL.toml", "md5": "d41d8cd98f00b204e9800998ecf8427e"}, {"path": "red4ext\\plugins\\ArchiveXL\\ArchiveXL.dll", "md5": "9c8507882bca35b45c1f6d646502599e"}, {"path": "red4ext\\plugins\\ArchiveXL\\LICENSE", "md5": "b2ac56eabdd1e9306010d9e8552faff9"}, {"path": "red4ext\\plugins\\ArchiveXL\\THIRD_PARTY_LICENSES", "md5": "127a03de85d2de411a6ce65d126f237e"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\ArchiveXL.archive", "md5": "88d879a057e09c2b06bb2e7e8395692f"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\Migration.xl", "md5": "1439a3a7e7a422e973fac31e88436709"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PhotoModeScope.xl", "md5": "4605a5603f44df830fb677c4df3e2d06"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerBaseScope.xl", "md5": "781480a6ec238d3d74f00cadfb358b2d"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationBeardFix.xl", "md5": "31dc4684220abc3fa0fcfc34c238a2b2"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationBeardScope.xl", "md5": "15cc9e250866fbcbf169a8c9ba3527d0"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationBrowsFix.xl", "md5": "fbc8e58d3778f28ab0ea3836eed95b67"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationBrowsPatch.xl", "md5": "e0c09647c2d65a8d5e65d7a3bff5defc"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationBrowsScope.xl", "md5": "d692292c01dad03559c71d348a3bc6eb"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationEyesFix.xl", "md5": "93fd8e0d06efc6c60a251e3b90d8b220"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationEyesPatch.xl", "md5": "ddc65c45813904129ca23ce2ae68fd54"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationEyesScope.xl", "md5": "7c5867b761af7e6593d93f3cc9635944"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationHairFix.xl", "md5": "5cb647ca016f17278bed550b81022531"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationHairPatch.xl", "md5": "b72b0a99634d1a4a9994c5e5f3007ef2"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationHairScope.xl", "md5": "317b882a1fee011c3b0fab528b1289bf"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationLashesFix.xl", "md5": "dcf3b018eff8eb90f4e2edc0028de050"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationLashesPatch.xl", "md5": "cf9fba79b3a1eed7950796814cfb3bde"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationLashesScope.xl", "md5": "1b609e2f87f087e3b13262a2a588e330"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\PlayerCustomizationScope.xl", "md5": "d41d8cd98f00b204e9800998ecf8427e"}, {"path": "red4ext\\plugins\\ArchiveXL\\Bundle\\QuestBaseScope.xl", "md5": "140a82527f2ebda7f1a57040ad33f339"}, {"path": "red4ext\\plugins\\ArchiveXL\\Scripts\\ArchiveXL.DynamicAppearance.reds", "md5": "d0773e3b158e7e52fb904783383190b0"}, {"path": "red4ext\\plugins\\ArchiveXL\\Scripts\\ArchiveXL.Global.reds", "md5": "9cb5bdd63e723f303656615e135bd2ab"}, {"path": "red4ext\\plugins\\ArchiveXL\\Scripts\\ArchiveXL.reds", "md5": "5bcc31285ef84e12d3f21894df25dbb1"}], "author": "psiberx", "details": {"category": "Modders Resources", "type": ""}, "phase": 0}, {"name": "Codeware", "version": "1.16.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7780, "fileId": 109168, "md5": "d03cc289904cf6e8c120df1a5fb8245e", "fileSize": 1899309, "logicalFilename": "Codeware", "updatePolicy": "exact", "tag": "FrhsazucK2"}, "hashes": [{"path": "red4ext\\plugins\\Codeware\\Codeware.dll", "md5": "c724b69e4702afa3ec3e74a732392dcc"}, {"path": "red4ext\\plugins\\Codeware\\LICENSE", "md5": "b2ac56eabdd1e9306010d9e8552faff9"}, {"path": "red4ext\\plugins\\Codeware\\THIRD_PARTY_LICENSES", "md5": "dd95464ff9666831f66511d0bfa90484"}, {"path": "red4ext\\plugins\\Codeware\\Data\\KnownHashes.txt", "md5": "280100118e944a335d22f65009c039f6"}, {"path": "red4ext\\plugins\\Codeware\\Scripts\\Codeware.Global.reds", "md5": "318f2e6689a028ec894270200eb48b5f"}, {"path": "red4ext\\plugins\\Codeware\\Scripts\\Codeware.Localization.reds", "md5": "4dbe40776a260030f0f8a358d3d0edb0"}, {"path": "red4ext\\plugins\\Codeware\\Scripts\\Codeware.reds", "md5": "b4019c029cecb34f4afa15d3052188db"}, {"path": "red4ext\\plugins\\Codeware\\Scripts\\Codeware.UI.reds", "md5": "494f73a54fa3621299ca12ab58c02d01"}, {"path": "red4ext\\plugins\\Codeware\\Scripts\\Codeware.UI.TextInput.reds", "md5": "90037cb470e2879611f22f1a616ab8cf"}], "author": "psiberx", "details": {"category": "Modders Resources", "type": ""}, "phase": 0}, {"name": "1. Better Movement - Jogging Allowed", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 20234, "fileId": 103574, "md5": "1c1e34d3d6aafc0f1afbf850c8851657", "fileSize": 826, "logicalFilename": "1. Better Movement - Jogging Allowed", "updatePolicy": "exact", "tag": "VS0iwl1JHc"}, "author": "Valour549", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "2. Disappearing Enemy Health Bar Fix (LHUD)", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19815, "fileId": 101786, "md5": "77a240084538d5338f5f0933b3db4d1e", "fileSize": 890, "logicalFilename": "2. Disappearing Enemy Health Bar Fix (LHUD)", "updatePolicy": "exact", "tag": "Rbr0UIBXdW"}, "author": "Valour549", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "3. Disappearing NPC and Vehicle Fix (Ultra Light)", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19628, "fileId": 103096, "md5": "35b56320388ae6fa1fd91ae84b16ecb0", "fileSize": 1258, "logicalFilename": "3. Disappearing NPC and Vehicle Fix (Ultra Light)", "updatePolicy": "exact", "tag": "4ty3oQqo2f"}, "author": "Valour549", "details": {"category": "Visuals and Graphics", "type": ""}, "phase": 1}, {"name": "3. <PERSON><PERSON> and <PERSON><PERSON>", "version": "1.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 18781, "fileId": 103661, "md5": "863996d5f95fa7ecd75e09da3d9519f1", "fileSize": 765709, "logicalFilename": "3. <PERSON><PERSON> and <PERSON><PERSON>", "updatePolicy": "exact", "tag": "tbVggceDHq"}, "author": "Valour549", "details": {"category": "Audio", "type": ""}, "phase": 1}, {"name": "Alternative Berserk", "version": "1.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 20393, "fileId": 104359, "md5": "91a84a65d8863234011df2ee8f741456", "fileSize": 6081, "logicalFilename": "Alternative Berserk", "updatePolicy": "exact", "tag": "Saef87vBkfM"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Base Fists and Arm Cyberware Attack Speed Fix", "version": "2.12.2024.04.09", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14130, "fileId": 74436, "md5": "b27265cae2bf6c9f5a08aff420cc6433", "fileSize": 2421, "logicalFilename": "Base Fists and Arm Cyberware Attack Speed Fix", "updatePolicy": "exact", "tag": "uHjSUbi1OEB"}, "author": "Metool", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Berserk Partial CD", "version": "cd-2.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14203, "fileId": 101417, "md5": "a5090d10af5517153d2d7ad9f70645cb", "fileSize": 2259, "logicalFilename": "Berserk Partial CD", "updatePolicy": "exact", "tag": "beoLXewjMff"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Berserk Unchained", "version": "2.3.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14203, "fileId": 104173, "md5": "507f454cbca73e43b050cc3ed18813a7", "fileSize": 4697, "logicalFilename": "Berserk Unchained", "updatePolicy": "exact", "tag": "MvwAyGlTq-n"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Berserk Unchained - Remove tunnelvision from VFX", "version": "0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14203, "fileId": 103079, "md5": "25217fe543f2c6b2180f3aabcb8ee43c", "fileSize": 6074, "logicalFilename": "Remove tunnelvision from VFX", "updatePolicy": "exact", "tag": "E7FWb50PHFj"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Better Chimera Mods", "version": "1.1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 18209, "fileId": 100249, "md5": "940b8000f30bfad1d5f9224bf1f7569c", "fileSize": 5026, "logicalFilename": "Better Chimera Mods", "updatePolicy": "exact", "tag": "gOD0BxR3g4T"}, "author": "Seijax", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Better Quality Sort", "version": "1.1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 20267, "fileId": 103914, "md5": "3b393592f418aa9440c6a709ae1d1002", "fileSize": 1769, "logicalFilename": "Better Quality Sort", "updatePolicy": "exact", "tag": "s1b3ZelhvlS"}, "author": "The guy posting it", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Black Chrome", "version": "1.1.8", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16031, "fileId": 104679, "md5": "f719c8c086fa0874508c29c4f2abf4ff", "fileSize": 142391, "logicalFilename": "Black Chrome", "updatePolicy": "exact", "tag": "PZxR8_vajvt"}, "author": "Phoenicia", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Bolt Shot FX Reset Fix", "version": "1.1.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19036, "fileId": 106297, "md5": "e7f2da6a2f8fa527b25d0905ad998007", "fileSize": 796, "logicalFilename": "Bolt Shot FX Reset Fix", "updatePolicy": "exact", "tag": "6-TX5YyJdFH"}, "author": "The guy posting it", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Buttslinger Quickmelee", "version": "2.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10556, "fileId": 102982, "md5": "7a58ce3f0faf2f27cd9656df112a3355", "fileSize": 4386, "logicalFilename": "Buttslinger Quickmelee", "updatePolicy": "exact", "tag": "oCE14W9Fv25"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON><PERSON>", "version": "1.2.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16092, "fileId": 100118, "md5": "d20d7ac3afa99e9981da311a1332b4e0", "fileSize": 4141, "logicalFilename": "<PERSON><PERSON><PERSON>", "updatePolicy": "exact", "tag": "9nBNVY23MW9"}, "author": "Seijax", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Cutscene Weapon Swapper", "version": "1.4.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 20743, "fileId": 107189, "md5": "b5e5920605993c64dcd9a60fd0a8a2f6", "fileSize": 3395, "logicalFilename": "Cutscene Weapon Swapper", "updatePolicy": "exact", "tag": "rCJi3oVYmOX"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Disappearing Enemy Health Bar Fix - Show Player Health Bar When Scanning", "version": "1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19815, "fileId": 103244, "md5": "9e7202b041447ed0ae2653baa1d16531", "fileSize": 747, "logicalFilename": "Show Player Health Bar When Scanning", "updatePolicy": "exact", "tag": "R-C9KKXK46C"}, "author": "Valour549", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Extra Berserks", "version": "1.2.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14236, "fileId": 104358, "md5": "33c4c4bb8fbc5e5aee311b075e41b06e", "fileSize": 10938, "logicalFilename": "Extra Berserks", "updatePolicy": "exact", "tag": "D37xWFF10yp"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Extra Hands", "version": "2.1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 12325, "fileId": 105066, "md5": "ae679005e1c9b0dc9a140f66a2281ad2", "fileSize": 51919, "logicalFilename": "Extra Hands", "updatePolicy": "exact", "tag": "XnrLAq5AWwv"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Extra Iconics", "version": "2.0.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15889, "fileId": 106489, "md5": "a7a9ce194422d240c859328bceab1d74", "fileSize": 10645700, "logicalFilename": "Extra Iconics", "updatePolicy": "exact", "tag": "vvEcXWo8hSc"}, "author": "Seijax", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Fix Advert Animations", "version": "1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 17726, "fileId": 107748, "md5": "082002ff69ae5ef77c0afeb48ab5a335", "fileSize": 130422, "logicalFilename": "Fix Advert Animations", "updatePolicy": "exact", "tag": "ErQFpJWUe90"}, "author": "nremind", "details": {"category": "Animations", "type": ""}, "phase": 1}, {"name": "gambling-system-blackjack", "version": "1.0.7", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19575, "fileId": 101176, "md5": "6141cdfd4f8d775f51a8cb0cc7ec2439", "fileSize": 440885, "logicalFilename": "gambling-system-blackjack", "updatePolicy": "exact", "tag": "8PgljbBIATT"}, "hashes": [{"path": "archive\\pc\\mod\\gambling-system-blackjack.archive", "md5": "e676e7d62c4ce8aec25d4fee50fd6fe5"}, {"path": "archive\\pc\\mod\\gambling-system-blackjack.xl", "md5": "8c2884ee96dd4e9e9bb9d6e47a39ac57"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\BlackjackMainMenu.lua", "md5": "189e81a4480976c8074c686d0aad2dce"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\CardEngine.lua", "md5": "1e187ef95e927b7a717c45c28743b6b5"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\HandCountDisplay.lua", "md5": "0418673d789cdbf2fe15feceffe35954"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\HolographicValueDisplay.lua", "md5": "90fdf56a20d2f559d02297d604202a57"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\init.lua", "md5": "4a29bc575676b4917749413f1ccdd440"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\SimpleCasinoChip.lua", "md5": "a10a04a2c9911a3b56f925e37b880cde"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\singleroundlogic.lua", "md5": "cb5c8d6a7417a7d96a34f83890ad3bff"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\SpotManager.lua", "md5": "85f2c8baaab7710fd92920e73e38daf1"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\External\\Cron.lua", "md5": "172f12af8b5a27b6947a30dc7c0684c6"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\External\\GameLocale.lua", "md5": "125a989daa354a792411e67271eb6dc6"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\External\\GameSession.lua", "md5": "7740879e3160a30fc055c9746c4a9174"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\External\\GameUI.lua", "md5": "e6e7b3857c65e3dfe1bf44fb9f065ec4"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\External\\interactionUI.lua", "md5": "381959a8050f293f8901b8198613ff25"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\External\\workspotUtils.lua", "md5": "a3bcfd6bf3429666fbf5812e03ec122c"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\External\\worldInteraction.lua", "md5": "7e6fc5f7321e56a043e9d82670f35f00"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\lang\\en-us.lua", "md5": "b55c210c7a57d0125bb7c9d190f70ebb"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-blackjack\\sessions\\null.txt", "md5": "d41d8cd98f00b204e9800998ecf8427e"}], "author": "Boe6", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "gambling-system-pachinko", "version": "1.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19889, "fileId": 106269, "md5": "aa1c4f85cadbec39bfce70f2f1680a09", "fileSize": 33097, "logicalFilename": "gambling-system-pachinko", "updatePolicy": "exact", "tag": "bYqQUQO8R0U"}, "hashes": [{"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-pachinko\\init.lua", "md5": "9b4d918d8d7e30f3ca2d349dc2d03914"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-pachinko\\JsonData.lua", "md5": "3575abc1275d3edde077d69d51d7894c"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-pachinko\\SpotManager.lua", "md5": "cf00ae193a7173e625ae34191eaf7e2f"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-pachinko\\addons\\null.txt", "md5": "d41d8cd98f00b204e9800998ecf8427e"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-pachinko\\External\\Cron.lua", "md5": "172f12af8b5a27b6947a30dc7c0684c6"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-pachinko\\External\\GameLocale.lua", "md5": "125a989daa354a792411e67271eb6dc6"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-pachinko\\External\\GameSession.lua", "md5": "7740879e3160a30fc055c9746c4a9174"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-pachinko\\External\\GameUI.lua", "md5": "e6e7b3857c65e3dfe1bf44fb9f065ec4"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-pachinko\\External\\interactionUI.lua", "md5": "381959a8050f293f8901b8198613ff25"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-pachinko\\lang\\en-us.lua", "md5": "dd109bcd5d717da417ccc916df73e0ef"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\gambling-system-pachinko\\sessions\\null.txt", "md5": "d41d8cd98f00b204e9800998ecf8427e"}], "author": "Boe6", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Gold Machetes for Valentinos", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19377, "fileId": 99680, "md5": "61234ba05d1b7472568a5a0110abf90a", "fileSize": 30171, "logicalFilename": "Gold Machetes for Valentinos", "updatePolicy": "exact", "tag": "3HzmL9_SW4z"}, "author": "<PERSON><PERSON><PERSON>", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Hammer tooltip fix", "version": "1.0.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 20217, "fileId": 103496, "md5": "39a0caadafcd49ce84f1a7e31a31787d", "fileSize": 478, "logicalFilename": "Hammer tooltip fix", "updatePolicy": "exact", "tag": "K14YA2uA-7y"}, "author": "The guy posting it", "details": {"category": "Miscellaneous", "type": ""}, "phase": 1}, {"name": "Inventory Adjustments Hub 1.1", "version": "1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19632, "fileId": 106543, "md5": "6b658ab1444d9ee6ea1a7ca6a5b2f74f", "fileSize": 100833, "logicalFilename": "Inventory Adjustments Hub 1.1", "updatePolicy": "exact", "tag": "k85Z01Naa6G"}, "author": "svar<PERSON>d", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "<PERSON>'s Machete - New Iconic Weapon", "version": "1.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19608, "fileId": 101516, "md5": "0bcdc0857c73813f582cc9ba5e5c2f5d", "fileSize": 4623380, "logicalFilename": "<PERSON>'s Machete - New Iconic Weapon", "updatePolicy": "exact", "tag": "1PE8KW54QNv"}, "author": "<PERSON><PERSON><PERSON>", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Lightning Projectile Launcher) - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.3.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 12591, "fileId": 100257, "md5": "f3519ea786bec21d96fecb9c12ee37ba", "fileSize": 38448, "logicalFilename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updatePolicy": "exact", "tag": "pJRjuw9kVvv"}, "author": "Seijax", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Keep Drawing The Line", "version": "3.4.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7198, "fileId": 106535, "md5": "80c4c9eda03eb0a465707b21acfe5756", "fileSize": 9636, "logicalFilename": "Keep Drawing The Line", "updatePolicy": "exact", "tag": "NhPboLQ85CE"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Limited HUD", "version": "2.18.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 2592, "fileId": 106534, "md5": "700d51f6ca77c4932d1de7d499d04517", "fileSize": 96744, "logicalFilename": "Limited HUD", "updatePolicy": "exact", "tag": "PPlqNwBV8lk"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Melee Attacks Fixes And Enhancements", "version": "0.37", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16921, "fileId": 104174, "md5": "d94ed52a8aae994b310c91907db9c013", "fileSize": 165377, "logicalFilename": "Melee Attacks Fixes And Enhancements", "updatePolicy": "exact", "tag": "XeqmttfUkc0"}, "author": "sosuineps<PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON> - Gallery Patch", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 21169, "fileId": 107415, "md5": "e222242796c5d13a20a4d6746db306e5", "fileSize": 1370, "logicalFilename": "<PERSON><PERSON> - Gallery Patch", "updatePolicy": "exact", "tag": "C8nvXNSdBF8"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON> - New Iconic Weapons", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 20073, "fileId": 103027, "md5": "062366e5a2d8155c242b864fc0b54f73", "fileSize": 5277587, "logicalFilename": "<PERSON><PERSON> - New Iconic Weapons", "updatePolicy": "exact", "tag": "JgAWUElUf27"}, "author": "<PERSON><PERSON><PERSON>", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON> - New Iconic Weapons", "version": "1.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19234, "fileId": 100028, "md5": "2bcd871c17d71915e96e72dfb31f53f7", "fileSize": 5186287, "logicalFilename": "<PERSON><PERSON> - New Iconic Weapons", "updatePolicy": "exact", "tag": "VjdT3hVfgyD"}, "author": "<PERSON><PERSON><PERSON>", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "NPC Vehicle Reactions Fixed", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19530, "fileId": 100327, "md5": "d11d6bdfdef32bad923b77bf83bee003", "fileSize": 475, "logicalFilename": "NPC Vehicle Reactions Fixed", "updatePolicy": "exact", "tag": "tBaecb9rNkM"}, "author": "icxrus", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Optical Camo Realism and Utility - Camo Partial CD", "version": "cd-2.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15308, "fileId": 101418, "md5": "e9b11cb0ab6ef3b1c68cca3414b27c57", "fileSize": 3716, "logicalFilename": "Camo Partial CD", "updatePolicy": "exact", "tag": "hoZvDLcqlQF"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Overclock Overheat", "version": "1.6.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14255, "fileId": 101473, "md5": "889e8a174e38d94d9cb04426852b0e9a", "fileSize": 7382, "logicalFilename": "Overclock Overheat", "updatePolicy": "exact", "tag": "oRub3zR6jSA"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON> (Remove Tint Glitches Scanlines and 3D Depth Effect - FOMOD) - <PERSON><PERSON>s", "version": "0.17.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10021, "fileId": 106354, "md5": "3385a3fa9b982401a82163793922e71b", "fileSize": 4148494, "logicalFilename": "<PERSON><PERSON>", "updatePolicy": "exact", "tag": "EFLcI1gAAsi"}, "author": "CyanideX", "details": {"category": "Visuals and Graphics", "type": ""}, "phase": 1}, {"name": "Pyromania Unchained", "version": "1.1.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19517, "fileId": 103105, "md5": "d12a7c84fe60009572d09b99f03d5519", "fileSize": 3897, "logicalFilename": "Pyromania Unchained", "updatePolicy": "exact", "tag": "a2XzFRhOYdO"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Quickhack Loadouts", "version": "1.4.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11682, "fileId": 105238, "md5": "419e6f68501fda460e0fa7b94a16228d", "fileSize": 18663, "logicalFilename": "Quickhack Loadouts", "updatePolicy": "exact", "tag": "mXvuFGbCLpr"}, "author": "RMK", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Quickhacks sort by slot", "version": "0.0.0.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11425, "fileId": 106066, "md5": "cb171591dbfeaff7f8613821fafb6a01", "fileSize": 1515, "logicalFilename": "Quickhacks sort by slot", "updatePolicy": "exact", "tag": "so42UOop-rI"}, "author": "nobo", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON>", "version": "1.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 21077, "fileId": 107064, "md5": "e870b19d3bcde97d03e7068fd985ea45", "fileSize": 3181, "logicalFilename": "<PERSON><PERSON>", "updatePolicy": "exact", "tag": "a0vhSOIXgfR"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Random Netrunners", "version": "1.2.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16475, "fileId": 101752, "md5": "5a8f3e1dfd8545385a5b0c7d77e244f4", "fileSize": 7065, "logicalFilename": "Random Netrunners", "updatePolicy": "exact", "tag": "khmsljlbHFY"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "ReflexIsCool", "version": "1.0.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15963, "fileId": 102307, "md5": "7911775ddb08f314ea6a0e6833128db7", "fileSize": 1700, "logicalFilename": "ReflexIsCool", "updatePolicy": "exact", "tag": "9uBEumxNPOg"}, "author": "Phoenicia", "details": {"category": "Modders Resources", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON> Redux", "version": "4.1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7197, "fileId": 100297, "md5": "5c3f430ff175321d0ac80fd275880218", "fileSize": 12227, "logicalFilename": "<PERSON><PERSON> Redux", "updatePolicy": "exact", "tag": "57FjeBUqvba"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Smarter <PERSON>per", "version": "2.3.5", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 2687, "fileId": 106194, "md5": "2207f82bad37d8647af8d4b253fc3bc4", "fileSize": 3680, "logicalFilename": "Smarter <PERSON>per", "updatePolicy": "exact", "tag": "5cJojZvOBZk"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON> - New Iconic Weapon", "version": "1.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 18595, "fileId": 102453, "md5": "ea98a00866146b3125432870c681ea84", "fileSize": 1327228, "logicalFilename": "<PERSON><PERSON> - New Iconic Weapon", "updatePolicy": "exact", "tag": "e1_6goPvdtv"}, "author": "<PERSON><PERSON><PERSON>", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "SynthDose", "version": "1.3.9", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14094, "fileId": 104344, "md5": "f21a3f4175049ee669bb42fed2cf69b6", "fileSize": 37612, "logicalFilename": "SynthDose", "updatePolicy": "exact", "tag": "gEW8O8SHQeC"}, "author": "Phoenicia", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Trace Position Overhaul", "version": "2.1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 12445, "fileId": 107822, "md5": "a2c649896090f9778c64e5e2d4020430", "fileSize": 9352, "logicalFilename": "Trace Position Overhaul", "updatePolicy": "exact", "tag": "76Ksn1mQA45"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON> - New Iconic Weapons", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 20827, "fileId": 107031, "md5": "01dcbe06934fb3c0ca9645cca90ba8b4", "fileSize": 9314561, "logicalFilename": "<PERSON><PERSON> - New Iconic Weapons", "updatePolicy": "exact", "tag": "tVan03p3whD"}, "author": "<PERSON><PERSON><PERSON>", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Cleaner <PERSON> and <PERSON><PERSON>", "version": "2.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10285, "fileId": 96913, "md5": "5af87543c94e8186b90dd03c15c979ad", "fileSize": 107530, "logicalFilename": "Cleaner <PERSON> and <PERSON><PERSON>", "updatePolicy": "exact", "tag": "Atp6eQQ5exa"}, "author": "heero139", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Equipment-EX", "version": "1.2.8", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 6945, "fileId": 99128, "md5": "46d650ab17cef9f38de9c787f36f4c7b", "fileSize": 89340, "logicalFilename": "Equipment-EX", "updatePolicy": "exact", "tag": "G9Au9QsiS1V"}, "author": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Appearance", "type": ""}, "phase": 1}, {"name": "Fighting Gangs Allowed - Reasonable Police", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19189, "fileId": 98549, "md5": "eafee4d562765d72a4f1e338696ae41f", "fileSize": 1159, "logicalFilename": "Fighting Gangs Allowed - Reasonable Police", "updatePolicy": "exact", "tag": "UJ3Ka922T-K"}, "author": "Valour549", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "DR-10 <PERSON><PERSON><PERSON>", "version": "1.0.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10541, "fileId": 59781, "md5": "42e99607eeec62835674c07333708fdb", "fileSize": 1164885, "logicalFilename": "DR-10 <PERSON><PERSON><PERSON>", "updatePolicy": "exact", "tag": "iemkYbK7I-D"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Filter Saves by Lifepath and Type", "version": "1.5.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 3400, "fileId": 96014, "md5": "2338f8d0b93e88c5166e7da95b818aa0", "fileSize": 13083, "logicalFilename": "Filter Saves by Lifepath and Type", "updatePolicy": "exact", "tag": "KdU9LX-jllJ"}, "author": "RMK", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Flashback Fixer 1.2", "version": "1.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16236, "fileId": 95071, "md5": "991233f1cf9cd072acd337ed51b085ba", "fileSize": 3839, "logicalFilename": "Flashback Fixer 1.2", "updatePolicy": "exact", "tag": "EUJ5Hqj-43V"}, "author": "jack", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Heat Converter", "version": "1.2.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11820, "fileId": 96848, "md5": "6740e9b66bb24adf5c5108f6a23891be", "fileSize": 1997, "logicalFilename": "Heat Converter", "updatePolicy": "exact", "tag": "CSK1ryGzkT1"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Modular <PERSON>bi Revamp - Precision Rifle", "version": "1.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14662, "fileId": 95094, "md5": "58ebf4d60b2db776fff7d274cd2ac445", "fileSize": 939, "logicalFilename": "Precision Rifle", "updatePolicy": "exact", "tag": "I-bRMAIffNY"}, "author": "Seijax", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "No Shooting Delay 1.1", "version": "1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15559, "fileId": 95076, "md5": "a34be2ff75cb068e8c982ba85a1d8da9", "fileSize": 4359, "logicalFilename": "No Shooting Delay 1.1", "updatePolicy": "exact", "tag": "A-8r9YdpO4g"}, "author": "jack", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Optical Camo Realism and Utility - Free Perk activations", "version": "perks-1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15308, "fileId": 96856, "md5": "b8e6542eb5bb78da81d575d6a6d6e194", "fileSize": 831, "logicalFilename": "Free Perk activations", "updatePolicy": "exact", "tag": "oOC7EDkRvQJ"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Plan C - Plan C - Japantown Stash Wall Fix", "version": "swf-1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 13461, "fileId": 96159, "md5": "22680f7bc220901b081ba7cea3bf078a", "fileSize": 17247, "logicalFilename": "Plan C - Japantown Stash Wall Fix", "updatePolicy": "exact", "tag": "HYvqHpGt_Aj"}, "author": "Demon9ne", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Slow Firing Rate on Longer Saves Bug Fix", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 18768, "fileId": 96837, "md5": "bf8548c44a4fe6917e28dbc1547d35a3", "fileSize": 1201, "logicalFilename": "Slow Firing Rate on Longer Saves Bug Fix", "updatePolicy": "exact", "tag": "9Ah3_IcsuNm"}, "author": "RMK", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Status Bar Bug Fixes", "version": "1.7", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4316, "fileId": 94842, "md5": "277c42de4c66f5c23346a578d7917e1b", "fileSize": 4931, "logicalFilename": "Status Bar Bug Fixes", "updatePolicy": "exact", "tag": "WJqSdyp63Vf"}, "author": "RMK", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON>", "version": "1.6.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9213, "fileId": 90279, "md5": "86e12bb5ce72a918a129e9d99d78aac1", "fileSize": 3592, "logicalFilename": "<PERSON><PERSON>", "updatePolicy": "exact", "tag": "ryJWesUrjXw"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Delete -A Few Weeks Tops- Voiceline 1.1", "version": "1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16932, "fileId": 89059, "md5": "0c9a6c81e4332f4f283bba57747b755c", "fileSize": 111423, "logicalFilename": "Delete -A Few Weeks Tops- Voiceline 1.1", "updatePolicy": "exact", "tag": "kon9Nal0KMY"}, "author": "fishnipples129 or Dane", "details": {"category": "Audio", "type": ""}, "phase": 1}, {"name": "Divided Faster Projectiles", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16950, "fileId": 88426, "md5": "4e41af50d0adced2a508486dc2bd7087", "fileSize": 484, "logicalFilename": "Divided Faster Projectiles", "updatePolicy": "exact", "tag": "NzdP0OHoO7m"}, "hashes": [{"path": "r6\\redundant\\tweaks\\Divided_Faster_Projectiles\\Items.Base_Sidewinder_Divided_SmartGun_SmartLink_Stats_inline12.yaml", "md5": "0193a9cd9efabc59ed55f75c7f63ff54"}], "author": "Tunnfisk", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Glen Apartment - Pool Table (8-Ball Fix)", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11107, "fileId": 58440, "md5": "438ebbe3e95074b9d6fb197cec4ad5eb", "fileSize": 21094, "logicalFilename": "Glen Apartment - Pool Table (8-Ball Fix)", "updatePolicy": "exact", "tag": "efywDSbS5J6"}, "author": "KickingWriter", "details": {"category": "Visuals and Graphics", "type": ""}, "phase": 1}, {"name": "Improved NCPD Map Filters", "version": "2.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 17488, "fileId": 91099, "md5": "be47bce82e1c386f2337dae9f60e30c8", "fileSize": 2953, "logicalFilename": "Improved NCPD Map Filters", "updatePolicy": "exact", "tag": "23gbjmKoWJw"}, "author": "v1ld", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "In Cold Blood Fix", "version": "1.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14880, "fileId": 88672, "md5": "6e456d4a69986b37d39a334e4a2dc7a2", "fileSize": 3880, "logicalFilename": "In Cold Blood Fix", "updatePolicy": "exact", "tag": "7_jU7ec4EXo"}, "hashes": [{"path": "r6\\scripts\\InColdBloodFix\\InColdBloodFix.reds", "md5": "e5eba14fde64c30ef30ad1e451fc2689"}], "author": "Demon9ne", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Intuitive Projectile Launcher System", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 13928, "fileId": 88686, "md5": "f326593e2f403a755a17d209d17ccabf", "fileSize": 2843, "logicalFilename": "Intuitive Projectile Launcher System", "updatePolicy": "exact", "tag": "Lmc2oZEn9mD"}, "author": "Paleforce", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Megingjord (hover legs cyberware) - Megingjord", "version": "1.3.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 12664, "fileId": 91811, "md5": "f9dd4b4822af6d5a94f419caf53ce873", "fileSize": 6385, "logicalFilename": "Megingjord", "updatePolicy": "exact", "tag": "0FNDDI0jKIG"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Pre 2.0 NCPD Scanner Icons", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 17442, "fileId": 90625, "md5": "78ab708240c23cb8267ddf76b3e7ac6f", "fileSize": 2597, "logicalFilename": "Pre 2.0 NCPD Scanner Icons", "updatePolicy": "exact", "tag": "B-TD_cMFyk0"}, "author": "v1ld", "details": {"category": "Miscellaneous", "type": ""}, "phase": 1}, {"name": "Quick Message Exit CET 1.0.3", "version": "1.0.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9377, "fileId": 91674, "md5": "540885490e545ab11fac37f22a19f359", "fileSize": 2720, "logicalFilename": "Quick Message Exit CET 1.0.3", "updatePolicy": "exact", "tag": "PoXYXeClnxD"}, "author": "anygoodname", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "React To <PERSON>", "version": "1.2.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16965, "fileId": 89659, "md5": "196a95db3add9bd1e6df50c45e131671", "fileSize": 13258, "logicalFilename": "React To <PERSON>", "updatePolicy": "exact", "tag": "WN7Eoxnrab8"}, "author": "<PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Sort Ripperdoc Inventory", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 17630, "fileId": 91393, "md5": "7e006d4405c9bdbbd7a932a9d5880fc1", "fileSize": 2320, "logicalFilename": "Sort Ripperdoc Inventory", "updatePolicy": "exact", "tag": "jPVDhFhr-9r"}, "author": "v1ld", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Stealth Remote Control Cars and Turrets - StealthRC.zip", "version": "1.1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9939, "fileId": 88860, "md5": "dd010a29c1a07fe3ce9ef6ffaf4c793b", "fileSize": 3419, "logicalFilename": "StealthRC.zip", "updatePolicy": "exact", "tag": "FjKXlIFufba"}, "author": "VeganForTheAnimals", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Weapon Handling Control", "version": "2.2.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11474, "fileId": 91810, "md5": "547b3860b01df4df171d11964816edd3", "fileSize": 8638, "logicalFilename": "Weapon Handling Control", "updatePolicy": "exact", "tag": "vdNY1uibg5i"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "CarDodgeStutterFix", "version": "0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16720, "fileId": 87519, "md5": "b4d1f7c1bca4eadf40a64bbecbdaa11a", "fileSize": 1683, "logicalFilename": "CarDodgeStutterFix", "updatePolicy": "exact", "tag": "PirlP9oSxGM"}, "author": "sosuineps<PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON> - NCPD Fixes", "version": "ncpdf-1.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14652, "fileId": 88315, "md5": "bfc2f030f06f778ed3d7027813cacc12", "fileSize": 2530, "logicalFilename": "<PERSON><PERSON> - NCPD Fixes", "updatePolicy": "exact", "tag": "oC2xse3OqE3"}, "author": "Demon9ne", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "DecalsFlickeringFix", "version": "0.12", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16579, "fileId": 87195, "md5": "dd1a7bfe522c69cd7681a92bcdb0a21f", "fileSize": 774, "logicalFilename": "DecalsFlickeringFix", "updatePolicy": "exact", "tag": "EMDR1vgZwq9"}, "author": "sosuineps<PERSON><PERSON><PERSON>", "details": {"category": "Visuals and Graphics", "type": ""}, "phase": 1}, {"name": "Gorilla Grapple", "version": "1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14183, "fileId": 87382, "md5": "9f13109847f8e9ee14cb8b5947ea7398", "fileSize": 4487, "logicalFilename": "Gorilla Grapple", "updatePolicy": "exact", "tag": "-nhhprwanu9"}, "hashes": [{"path": "archive\\pc\\mod\\Gorilla Grapple.archive", "md5": "5ef908d1f26a1732cb0ffdb3ebc3959d"}, {"path": "archive\\pc\\mod\\Gorilla_Grapple.archive.xl", "md5": "7784e025b8ea59e03906d5d71665a836"}, {"path": "r6\\scripts\\Gorilla_Grapple\\Gorilla_Grapple.reds", "md5": "018b62d1b4bf5a5da840f53cf5fc795b"}, {"path": "r6\\tweaks\\Gorilla_Grapple\\Gorilla_Grapple.yaml", "md5": "9ae0b7492532bf5819b7549bbfd175f6"}], "author": "Demon9ne", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Looting QoL", "version": "1.6", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14730, "fileId": 87365, "md5": "9624f57a02f66ac6b9a88fa65cdb04a3", "fileSize": 4017, "logicalFilename": "Looting QoL", "updatePolicy": "exact", "tag": "xqILPBb6y-P"}, "hashes": [{"path": "archive\\pc\\mod\\LootingQoL.archive", "md5": "b6779ace385e335f5af39f330b0b5428"}, {"path": "archive\\pc\\mod\\LootingQoL.archive.xl", "md5": "83c879cdc4fc048e6ba090f5c1cfdcb8"}, {"path": "r6\\scripts\\LootingQoL\\LootingQoL.reds", "md5": "4743a1cebd2b4d273cde144f84ef4ffc"}], "author": "Demon9ne", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Passenger Targeting Fix", "version": "1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16541, "fileId": 87366, "md5": "930bc75de1e5a3899f0b8e9ec8db4994", "fileSize": 1374, "logicalFilename": "Passenger Targeting Fix", "updatePolicy": "exact", "tag": "lf5ghffs7sC"}, "hashes": [{"path": "r6\\scripts\\PassengerTargetingFix\\PassengerTargetingFix.reds", "md5": "44d4f0d1e21dbb5fda8ea0b1a1be91e6"}], "author": "Demon9ne", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "rasetsu screens bug fix", "version": "*******", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16896, "fileId": 88239, "md5": "0988f83580c48c84025d9f67cc6cb4bd", "fileSize": 19013, "logicalFilename": "rasetsu screens bug fix", "updatePolicy": "exact", "tag": "Ypu-W054quZ"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Miscellaneous", "type": ""}, "phase": 1}, {"name": "Stash Filters", "version": "2.1.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 5298, "fileId": 86509, "md5": "7761e4dc7bd1ef54b17f4f61be9d75c6", "fileSize": 1287, "logicalFilename": "Stash Filters", "updatePolicy": "exact", "tag": "wxV8aYi-RuM"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Challenging Breach Minigame", "version": "1.3.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 3661, "fileId": 87057, "md5": "a92e3e770824ac9b5a29613595d25de9", "fileSize": 14279, "logicalFilename": "Challenging Breach Minigame", "updatePolicy": "exact", "tag": "tv1UWKavyA_"}, "author": "RMK", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Dash Fix", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16272, "fileId": 84895, "md5": "41a7dee22c9072ffe0615ddd0ffc8a0b", "fileSize": 3658, "logicalFilename": "Dash Fix", "updatePolicy": "exact", "tag": "WacHSu_6sg_"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "EddiesNotificationFix 1.01", "version": "1.01", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16420, "fileId": 85857, "md5": "18b444a0a7fe9759d8bab1ccbe745803", "fileSize": 769, "logicalFilename": "EddiesNotificationFix 1.01", "updatePolicy": "exact", "tag": "EAZ98M1F2en"}, "author": "Atticus", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "LessSamurai-v1.3", "version": "1.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16129, "fileId": 84704, "md5": "deee88e441c5cb03ab1cab7d02596899", "fileSize": 5244, "logicalFilename": "LessSamurai-v1.3", "updatePolicy": "exact", "tag": "55ead892pPV"}, "author": "Mister<PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Replace Weapon Mods", "version": "1.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15409, "fileId": 86153, "md5": "d3c56546885a3ac2d1f7cc3fb73ff45c", "fileSize": 2336, "logicalFilename": "Replace Weapon Mods", "updatePolicy": "exact", "tag": "gCK--8C7PrH"}, "author": "RMK", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Stand After Sliding", "version": "0.1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16311, "fileId": 86817, "md5": "762a5bf1568b416d8cbd28c4531adfb1", "fileSize": 963, "logicalFilename": "Stand After Sliding", "updatePolicy": "exact", "tag": "bhRtPYPeCIS"}, "author": "ByteBlitzer", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Body Shield", "version": "1.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10533, "fileId": 84018, "md5": "5005502d28fdad13522c747d3243f025", "fileSize": 1247, "logicalFilename": "Body Shield", "updatePolicy": "exact", "tag": "z8CF7s_mMsm"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "dynamic_wanted_stars_v1.0", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15449, "fileId": 80915, "md5": "b9fe1aeaae996e5310cf05ada07de8b3", "fileSize": 3156, "logicalFilename": "dynamic_wanted_stars_v1.0", "updatePolicy": "exact", "tag": "QcuTFL0l4gv"}, "author": "Mister<PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Gambling Props - Poker Chips and Playing Cards - Gambling Props", "version": "1.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15229, "fileId": 80919, "md5": "4f93d41a5693f37252601e12f8cec509", "fileSize": 108185771, "logicalFilename": "Gambling Props", "updatePolicy": "exact", "tag": "pzCuIoBeh-x"}, "author": "Boe6", "details": {"category": "Miscellaneous", "type": ""}, "phase": 1}, {"name": "Modular <PERSON> Revamp - Damage increase", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14662, "fileId": 81335, "md5": "edcb610d1a16323d64da1d332b842c92", "fileSize": 482, "logicalFilename": "Damage increase", "updatePolicy": "exact", "tag": "BhqQ56Xiw54"}, "author": "Seijax", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "AdaptiveSliders", "version": "2024-06-10", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 5075, "fileId": 79848, "md5": "8a7314a5fb27bb6c57d92d04f911e36f", "fileSize": 1405, "logicalFilename": "AdaptiveSliders", "updatePolicy": "exact", "tag": "I0eY-XYMbzJ"}, "author": "DeepBlueFrog", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Backpack Search", "version": "1.2.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14264, "fileId": 78386, "md5": "0cab510afb53d6a2c3cf368e117aeae1", "fileSize": 1717, "logicalFilename": "Backpack Search", "updatePolicy": "exact", "tag": "6ZgSekSdX1v"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Damage Scaling and Balance (Formerly Level Scaling) - Damage Scaling", "version": "4.2.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 1712, "fileId": 80583, "md5": "3360835d4e78b52842074b319915221c", "fileSize": 29978, "logicalFilename": "Damage Scaling", "updatePolicy": "exact", "tag": "Bmwbog-uI6c"}, "author": "RMK", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Gamepad Button Hold Indicator Fix", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 13780, "fileId": 72518, "md5": "c227ae85f8a55fd44b13a1256e4347d1", "fileSize": 1374, "logicalFilename": "Gamepad Button Hold Indicator Fix", "updatePolicy": "exact", "tag": "B4MELnsTGEW"}, "hashes": [{"path": "r6\\scripts\\Gamepad Button Hold Indicator Fix\\GamepadButtonHoldIndicatorFix.reds", "md5": "383e436be7d1c4fad40c634f650df065"}], "author": "Demon9ne", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Kiroshi Night Vision Mod 1.81", "version": "1.81", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 8326, "fileId": 80782, "md5": "c812497c7e328d9dbc59401d7e9881d2", "fileSize": 60545, "logicalFilename": "Kiroshi Night Vision Mod 1.81", "updatePolicy": "exact", "tag": "qi_XDEVOLsK"}, "author": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Sensible Time Bomb", "version": "1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10575, "fileId": 77306, "md5": "6511e811a65e08afaa4090d1d73f1675", "fileSize": 595, "logicalFilename": "Sensible Time Bomb", "updatePolicy": "exact", "tag": "gvzUzPgWiqK"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Slaught-<PERSON><PERSON><PERSON><PERSON>", "version": "1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14125, "fileId": 78705, "md5": "3542277a6a5d99f794c400f832f597b0", "fileSize": 12211, "logicalFilename": "Slaught-<PERSON><PERSON><PERSON><PERSON>", "updatePolicy": "exact", "tag": "Cw0P5PqIA57"}, "hashes": [{"path": "archive\\pc\\mod\\Slaughtomatic QoL.archive", "md5": "38789d2c5b9842183188d124c00d9ea2"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\AppearanceMenuMod\\Collabs\\Custom Props\\Slaughtomatic_QoL\\Slaughtomatic_QoL_AMM.lua", "md5": "4335c4d56554c91ce6d56c0ba12ffd92"}, {"path": "r6\\scripts\\Slaughtomatic_QoL\\Slaughtomatic_QoL.reds", "md5": "a73c3b69a8ff97b6df2295cb5f3bbc54"}], "author": "Demon9ne", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Stash Search", "version": "1.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14264, "fileId": 77762, "md5": "4e17e6094f952619c47e69eac07a46bb", "fileSize": 1907, "logicalFilename": "Stash Search", "updatePolicy": "exact", "tag": "bYlN7s8c79S"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Actual Chrome Compression", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14044, "fileId": 73859, "md5": "9e47177604a5580245a2a90cc8cc8ea9", "fileSize": 1908, "logicalFilename": "Actual Chrome Compression", "updatePolicy": "exact", "tag": "8oya8pI0U3n"}, "author": "<PERSON><PERSON>z", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Bug Fix - Full-Auto Tech Weapons Bolt Duration - Senkoh LX Bolt Fix", "version": "2.12", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14053, "fileId": 73923, "md5": "29266638cc452f0e5a9cec0d96280ce5", "fileSize": 469, "logicalFilename": "Senkoh LX Bolt Fix", "updatePolicy": "exact", "tag": "yEW7J0Qq3JI"}, "author": "Metool", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "<PERSON> Regen", "version": "2.12", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14223, "fileId": 74865, "md5": "e820d5c53f15d6e3e63162436414f9c8", "fileSize": 414, "logicalFilename": "<PERSON> Regen", "updatePolicy": "exact", "tag": "qAnxG7WiePF"}, "author": "Metool", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Malorian Arms 3516 - Pre2.0 ADS Speedx1.5", "version": "1.3.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9666, "fileId": 61026, "md5": "1e6a311fec3614f8b17e3addcee5b250", "fileSize": 1187113, "logicalFilename": "Malorian Arms 3516 - Pre2.0 ADS Speedx1.5", "updatePolicy": "exact", "tag": "mXbr6sY8xZw"}, "author": "Azagorh", "details": {"category": "Animations", "type": ""}, "phase": 1}, {"name": "Malorian Arms 3516 - Slide Fix", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9666, "fileId": 51470, "md5": "e42c42336bbfb434aaa9429a06c7198a", "fileSize": 7514, "logicalFilename": "Malorian Arms 3516 - Slide Fix", "updatePolicy": "exact", "tag": "O7vM9AJbKoQ"}, "author": "Azagorh", "details": {"category": "Animations", "type": ""}, "phase": 1}, {"name": "Plan C", "version": "1.4", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 13461, "fileId": 76445, "md5": "06d296357cc0569c6f602e99ebda1503", "fileSize": 554806, "logicalFilename": "Plan C", "updatePolicy": "exact", "tag": "dpSV0UlDiss"}, "hashes": [{"path": "archive\\pc\\mod\\Plan_C.archive", "md5": "f216eef86994b9519f397fab81ecddf5"}, {"path": "archive\\pc\\mod\\Plan_C.archive.xl", "md5": "7fb500e81323f9a77f0d6e6d8cff5f9b"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\AppearanceMenuMod\\Collabs\\Custom Props\\Plan_C\\Plan_C_AMM.lua", "md5": "b9572f2c92d07f02639698b76e9b135f"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\Plan_C\\init.lua", "md5": "e8773abcf17d70a88ba177bc94e97ac5"}, {"path": "r6\\scripts\\Plan_C\\Liberty_Plan_C.reds", "md5": "b9c688ddac7755e3a985b51e78627de5"}, {"path": "r6\\scripts\\Plan_C\\Liberty_Plan_C_gui.reds", "md5": "9e7018dd43e75c32dbd7780912aef47d"}, {"path": "r6\\tweaks\\Plan_C\\Plan_C.yaml", "md5": "4b69f91bf195b2e13b7d7f0fa8288ede"}], "author": "Demon9ne", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Recon Grenades Bounce Fix", "version": "2.12", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14163, "fileId": 74564, "md5": "e0f7dc7e4f54111e73d2917ed8ba4042", "fileSize": 710, "logicalFilename": "Recon Grenades Bounce Fix", "updatePolicy": "exact", "tag": "ujxLr-WmDO4"}, "author": "Metool", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Renaissance Punk Scaling", "version": "2.12.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14037, "fileId": 73928, "md5": "31b7a5c080b60ebe710e4911cb30d096", "fileSize": 2527, "logicalFilename": "Renaissance Punk Scaling", "updatePolicy": "exact", "tag": "a6s05uu2WtX"}, "author": "Metool", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Retrothrusters QoL", "version": "1.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14320, "fileId": 75791, "md5": "7a18389c3cff091c2e50c21a73ce0b9b", "fileSize": 5901, "logicalFilename": "Retrothrusters QoL", "updatePolicy": "exact", "tag": "_WVSJ9QDyPu"}, "hashes": [{"path": "archive\\pc\\mod\\Retrothrusters_QoL.archive", "md5": "9ec749c52171ce4c459bd0043ecc31dc"}, {"path": "archive\\pc\\mod\\Retrothrusters_QoL.archive.xl", "md5": "a1a853eca426aae1318adf0acd132ebe"}, {"path": "r6\\scripts\\Retrothrusters_QoL\\Retrothrusters_QoL.reds", "md5": "973d34fbd2fcefa57899273c33b505b7"}, {"path": "r6\\tweaks\\Retrothrusters_QoL\\Retrothrusters_QoL.yaml", "md5": "4f1a77a3cac0aec99f5b0626ab939a79"}], "author": "Demon9ne", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Slaught-O-Matic Platinum Semi-Auto", "version": "1.5", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14004, "fileId": 74868, "md5": "ec46a3bff31a2f0ce860043778ef0011", "fileSize": 1061487, "logicalFilename": "Slaught-O-Matic Platinum Semi-Auto", "updatePolicy": "exact", "tag": "lJ-liPteTUc"}, "hashes": [{"path": "archive\\pc\\mod\\Slaught-O-Matic Platinum Semi-Auto.archive", "md5": "e1c0902ffba7a84baf47c16e70825d10"}, {"path": "archive\\pc\\mod\\Slaughtomatic_Platinum.archive.xl", "md5": "af114a9388ee39a5167137242c01977d"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\AppearanceMenuMod\\Collabs\\Custom Props\\Slaughtomatic_Platinum\\Slaughtomatic_Platinum_AMM.lua", "md5": "ba72a2980d124df2376b1c7d9e9e7668"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\Slaughtomatic_Platinum\\init.lua", "md5": "e81ebad64395747b6edfcd46d006366d"}, {"path": "r6\\scripts\\Slaughtomatic_Platinum\\Slaughtomatic_Platinum.reds", "md5": "e069fd82ad1f99856bd257bc86768de0"}, {"path": "r6\\scripts\\Slaughtomatic_Platinum\\Slaughtomatic_Platinum_QoL.reds", "md5": "c77f54eeea8828a5d723948afdc0f826"}, {"path": "r6\\tweaks\\Slaughtomatic_Platinum\\Slaughtomatic_Platinum.yaml", "md5": "5917afb14fc6bf589d9d5913d900a3db"}], "author": "Demon9ne", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Clean Computer Wallpaper", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 13657, "fileId": 71710, "md5": "bdc8516a5ea2e898ba774e43e46818ce", "fileSize": 2917089, "logicalFilename": "Clean Computer Wallpaper", "updatePolicy": "exact", "tag": "BiFO2QeLQc-"}, "author": "<PERSON><PERSON><PERSON>", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Cronos (New Iconic Weapon) - Cronos", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 13462, "fileId": 70766, "md5": "aeaf80f975f0394cbd3f4bd622eb3467", "fileSize": 1578913, "logicalFilename": "Cronos", "updatePolicy": "exact", "tag": "q4J7HXjIEGe"}, "author": "Tron-117", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Enable_Advert_Animations", "version": "2.11", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 13014, "fileId": 68444, "md5": "7e89cda4ee4f288e2bee312c761a1015", "fileSize": 2443993, "logicalFilename": "Enable_Advert_Animations", "updatePolicy": "exact", "tag": "aKZfVVf1svq"}, "author": "EmreHQ", "details": {"category": "Animations", "type": ""}, "phase": 1}, {"name": "Immersion patch - The Hunt quest missing audio restore - sq021_randy_pc_scene CET 2.0.3", "version": "2.0.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7413, "fileId": 71492, "md5": "f0797d6b839a30034616cc9957d3bb7c", "fileSize": 3120, "logicalFilename": "sq021_randy_pc_scene CET 2.0.3", "updatePolicy": "exact", "tag": "WCz0RNGNdPq"}, "author": "anygoodname", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "<PERSON>izarre Ball<PERSON>s - Over Compensator", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 13005, "fileId": 68461, "md5": "62458a82ac350fd7205800ccfceea0a0", "fileSize": 2997242, "logicalFilename": "<PERSON>izarre Ball<PERSON>s - Over Compensator", "updatePolicy": "exact", "tag": "yM3NzVdVvXH"}, "author": "<PERSON><PERSON> and b<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "E3 Smart Windows 1.1", "version": "1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7026, "fileId": 68540, "md5": "cbd6aa8c16e738ad149b68f01bad8830", "fileSize": 634565, "logicalFilename": "E3 Smart Windows 1.1", "updatePolicy": "exact", "tag": "9OVWRhk-NVD"}, "author": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Playable Arcade Machines 1.4", "version": "1.4", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4213, "fileId": 68532, "md5": "1a201cb08f6261c4ce03f26b99ca4dce", "fileSize": 1525969, "logicalFilename": "Playable Arcade Machines 1.4", "updatePolicy": "exact", "tag": "B50-sWdMW7c"}, "author": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Deadeye No Sound", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 12787, "fileId": 67201, "md5": "4023e9914a4f47e973cfac0e11353038", "fileSize": 497, "logicalFilename": "Deadeye No Sound", "updatePolicy": "exact", "tag": "Ckx472fNypT"}, "author": "Seijax", "details": {"category": "Audio", "type": ""}, "phase": 1}, {"name": "Default Edition - Midas Collection", "version": "1.0.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11800, "fileId": 67602, "md5": "616a80afa7d85bc72dc7a7708f83aaec", "fileSize": 5938869, "logicalFilename": "Default Edition - Midas Collection", "updatePolicy": "exact", "tag": "sgWPQEaSr0i"}, "author": "<PERSON><PERSON>", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "GLEIPNIR", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 12695, "fileId": 66740, "md5": "9fbf72f529f07c4c355536ba59b45425", "fileSize": 1859593, "logicalFilename": "GLEIPNIR", "updatePolicy": "exact", "tag": "qtJ2ZnHC4Bi"}, "author": "<PERSON><PERSON>", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Quickhack Hotkeys", "version": "2.1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7238, "fileId": 66715, "md5": "32888b5d6b1418d396debe51de70501b", "fileSize": 58878, "logicalFilename": "Quickhack Hotkeys", "updatePolicy": "exact", "tag": "KfhEsvNig8A"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Crafting Recipe Owned Labels", "version": "1.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11261, "fileId": 59132, "md5": "e98caf6ca3466cf08f812d0f0ac7cf36", "fileSize": 1592, "logicalFilename": "Crafting Recipe Owned Labels", "updatePolicy": "exact", "tag": "NBiGQNubarp"}, "author": "RMK", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "MaxTac Silencer", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11979, "fileId": 63048, "md5": "591c7a1dd6db2be19e9955c8b78c3e3d", "fileSize": 1497277, "logicalFilename": "MaxTac Silencer", "updatePolicy": "exact", "tag": "asmCsA0Wh5M"}, "author": "Tron-117", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10830, "fileId": 57042, "md5": "a1b72413f3168c477eead6e1592153b4", "fileSize": 2213939, "logicalFilename": "<PERSON><PERSON><PERSON><PERSON>", "updatePolicy": "exact", "tag": "RGGJBZFGl8R"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "No Crowd panic from devices CET 1.2.0", "version": "1.2.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7353, "fileId": 39456, "md5": "d8b511309a1d416e0946dc85d6ac92b6", "fileSize": 1912, "logicalFilename": "No Crowd panic from devices CET 1.2.0", "updatePolicy": "exact", "tag": "liznhn1DRH9"}, "author": "anygoodname", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "No Crowd panic from stealth activity CET 1.0.0", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7360, "fileId": 38867, "md5": "f649b0b1da848f1d0aef808a413279ff", "fileSize": 2316, "logicalFilename": "No Crowd panic from stealth activity CET 1.0.0", "updatePolicy": "exact", "tag": "xrvI0YTHEve"}, "author": "anygoodname", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "NoExitCrouchWithStealthMeleeAttacks-1.01.zip", "version": "1.01", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4470, "fileId": 24776, "md5": "d947de5cb2da5369ba5b43c67a5cb51f", "fileSize": 1242, "logicalFilename": "NoExitCrouchWithStealthMeleeAttacks-1.01.zip", "updatePolicy": "exact", "tag": "MNjKCJs49mY"}, "hashes": [{"path": "r6\\redundant\\NoExitCrouchWithStealthMeleeAttacks.reds", "md5": "694bcf51b7333b03648f53ad1e6455ce"}], "author": "Paradox4624", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Radio Port Vehicle Fix 1.1.1", "version": "1.1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11757, "fileId": 62493, "md5": "20c46081ea180de8a7a5cbeb183ef982", "fileSize": 3023, "logicalFilename": "Radio Port Vehicle Fix 1.1.1", "updatePolicy": "exact", "tag": "VGIPgMia59G"}, "author": "mar", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Second Heart Fix", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11100, "fileId": 61263, "md5": "f06173df4df18a2ff23b9ab4f5a84796", "fileSize": 1766, "logicalFilename": "Second Heart Fix", "updatePolicy": "exact", "tag": "77LKIKb_Xya"}, "author": "enowai", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Allow Unbound Actions (Keybinds)", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11429, "fileId": 59998, "md5": "36866f6060a02b7b7be9e15292cdd12e", "fileSize": 712, "logicalFilename": "Allow Unbound Actions (Keybinds)", "updatePolicy": "exact", "tag": "BDMDzKl7lkv"}, "author": "RMK", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON>ll <PERSON>", "version": "1.4.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 2886, "fileId": 60146, "md5": "281f4baaa5c81faab552ba729e0559c3", "fileSize": 14466, "logicalFilename": "<PERSON><PERSON>ll <PERSON>", "updatePolicy": "exact", "tag": "CkUEntfm1F7"}, "author": "RMK", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Real Vendor Names", "version": "2.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4941, "fileId": 60088, "md5": "c7a7b1a740f9dd68dbc3c0b6dc7f515c", "fileSize": 1735, "logicalFilename": "Real Vendor Names", "updatePolicy": "exact", "tag": "e1hdznDdRNL"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Silent Silencers and Throwing Knives", "version": "1.6.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4070, "fileId": 60385, "md5": "357537e32d058ee70f59da7f7882c493", "fileSize": 22611, "logicalFilename": "Silent Silencers and Throwing Knives", "updatePolicy": "exact", "tag": "DdguzFCxQBB"}, "author": "RMK", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Simple XP Multiplier", "version": "2.4", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 3136, "fileId": 60152, "md5": "b218a4f5d436cbfa2ebf813aff58dd23", "fileSize": 13096, "logicalFilename": "Simple XP Multiplier", "updatePolicy": "exact", "tag": "92QF2SlbO4F"}, "author": "RMK", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Zoomable Scopes 1.4", "version": "1.4", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 3543, "fileId": 60032, "md5": "2c4dbd0a93c5c9ec2b12e30e916e07e2", "fileSize": 10020, "logicalFilename": "Zoomable Scopes 1.4", "updatePolicy": "exact", "tag": "VU-Gu6J0KuV"}, "author": "k<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Midnight Arms MA70 BSG", "version": "1.1.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10252, "fileId": 57029, "md5": "cd3206cb35cd875fd84d2652bfebb784", "fileSize": 1779878, "logicalFilename": "Midnight Arms MA70 BSG", "updatePolicy": "exact", "tag": "ffgRnKJy-OU"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "No Camera Auto Centering", "version": "0.9", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 2169, "fileId": 57715, "md5": "e4e49bda15f460ebdbd3e40e5a0425a1", "fileSize": 800, "logicalFilename": "No Camera Auto Centering", "updatePolicy": "exact", "tag": "_FwkhRFhIhz"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "accesspointrewards", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10355, "fileId": 54427, "md5": "5402f6a3c5fca758926d37533ff97653", "fileSize": 1455, "logicalFilename": "accesspointrewards", "updatePolicy": "exact", "tag": "MwcciznGf3B"}, "author": "rfuzzo", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON><PERSON>", "version": "1.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9707, "fileId": 56508, "md5": "cb97e3fe0b823db203778ca186d91793", "fileSize": 1111, "logicalFilename": "<PERSON><PERSON><PERSON>", "updatePolicy": "exact", "tag": "oeI-F_6W9pe"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Disable W-S Keys To Select Dialog Options", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9799, "fileId": 51994, "md5": "77301908caa448b0fb52258c01842b22", "fileSize": 725, "logicalFilename": "Disable W-S Keys To Select Dialog Options", "updatePolicy": "exact", "tag": "MN_-QO-KMKe"}, "author": "K4DAV3R", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Faster <PERSON><PERSON><PERSON> - 10 days", "version": "2.0.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 5112, "fileId": 54866, "md5": "a02887d55ea1723632cecdd311446c38", "fileSize": 6477, "logicalFilename": "Faster <PERSON><PERSON><PERSON> - 10 days", "updatePolicy": "exact", "tag": "B00zBU-tpNy"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Miscellaneous", "type": ""}, "phase": 1}, {"name": "Focus Vignette Removal", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10712, "fileId": 56433, "md5": "a2ec3f4c2badd83bcb54aac7df5ac720", "fileSize": 4765, "logicalFilename": "Focus Vignette Removal", "updatePolicy": "exact", "tag": "wiYwlvmuHVJ"}, "author": "Seijax", "details": {"category": "Visuals and Graphics", "type": ""}, "phase": 1}, {"name": "<PERSON>de Read Shards", "version": "0.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 2820, "fileId": 26975, "md5": "4c0b6d1e90bfa0e2dfbb309b959f034f", "fileSize": 1661, "logicalFilename": "<PERSON>de Read Shards", "updatePolicy": "exact", "tag": "CLNTF9ir156"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Miscellaneous", "type": ""}, "phase": 1}, {"name": "Instant Wardrobe", "version": "v1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 6550, "fileId": 34285, "md5": "be0266a1c65a7e1ca1c02f5533d8f2cf", "fileSize": 47247, "logicalFilename": "Instant Wardrobe", "updatePolicy": "exact", "tag": "NNkP1PYb4xy"}, "author": "Spicy", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Vehicle Summon Tweaks - Repair Cost", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4658, "fileId": 25673, "md5": "ce480a3de090c5b0e0e2c3a3cfb754f9", "fileSize": 715, "logicalFilename": "Vehicle Summon Tweaks - Repair Cost", "updatePolicy": "exact", "tag": "wVcLvjSAJZR"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Better Armor Tooltips", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9706, "fileId": 51586, "md5": "e1eef6c6075f97c22988173b470ff92f", "fileSize": 6574, "logicalFilename": "Better Armor Tooltips", "updatePolicy": "exact", "tag": "EkaRmxPS1Lc"}, "author": "RMK", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Enable <PERSON><PERSON><PERSON> - v0.1", "version": "0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9496, "fileId": 50640, "md5": "cd681249da5c16612630714a717957dc", "fileSize": 857, "logicalFilename": "v0.1", "updatePolicy": "exact", "tag": "D8OuOSov8n3"}, "author": "Kvalyr", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Don't Hide Stamina Bar on Holster", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9448, "fileId": 50455, "md5": "a5edf06afff8f3b0ad11ca58e80e2cb0", "fileSize": 669, "logicalFilename": "Don't Hide Stamina Bar on Holster", "updatePolicy": "exact", "tag": "MUb-S-F8sjA"}, "author": "RMK", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "More Climbable Objects", "version": "1.02", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7043, "fileId": 50160, "md5": "769a61bf1e1918c84c6cfac51bce7c8d", "fileSize": 3241, "logicalFilename": "More Climbable Objects", "updatePolicy": "exact", "tag": "ZQUW9r7U-kq"}, "author": "SOSUINEPSIXUYU", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "No Paper Bags From Vending Machines", "version": "1.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 8387, "fileId": 45239, "md5": "0486fca0cbe143265f0bcb44f6822fd3", "fileSize": 91651, "logicalFilename": "No Paper Bags From Vending Machines", "updatePolicy": "exact", "tag": "_JJPksqeuFH"}, "author": "johnson", "details": {"category": "Visuals and Graphics", "type": ""}, "phase": 1}, {"name": "Not as much Faster Vehicle Knockback Animation", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 5431, "fileId": 29149, "md5": "b14c86901394da342d5ba7bfd07f4e54", "fileSize": 531, "logicalFilename": "Not as much Faster Vehicle Knockback Animation", "updatePolicy": "exact", "tag": "b17-nHroIGx"}, "author": "<PERSON><PERSON>Stuff", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Clear Skill Checks", "version": "1.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 2805, "fileId": 36692, "md5": "83d142cdca6e58f83c1995564ef12fb5", "fileSize": 1755, "logicalFilename": "Clear Skill Checks", "updatePolicy": "exact", "tag": "8qyl2C2sUPT"}, "author": "RMK", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Crouch vignette effect remover 2.0", "version": "2.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 535, "fileId": 49847, "md5": "118389beae07751e60a708114d09c211", "fileSize": 2004, "logicalFilename": "Crouch vignette effect remover 2.0", "updatePolicy": "exact", "tag": "u4MLSwbner0"}, "author": "OREZXX", "details": {"category": "Miscellaneous", "type": ""}, "phase": 1}, {"name": "Spicy Clean Hacking Screen 0.11 (FOMOD)", "version": "0.11", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 906, "fileId": 9719, "md5": "ce6af07a2f2f77b850f62a8d6c59914d", "fileSize": 2546617, "logicalFilename": "Spicy Clean Hacking Screen 0.11 (FOMOD)", "updatePolicy": "exact", "tag": "LUWn9NpcpCj"}, "choices": {"type": "fomod", "options": [{"name": "Introduction", "groups": [{"name": "Intro", "choices": [{"name": " Spicy Clean Hacking Screen", "idx": 0}]}]}, {"name": "Options", "groups": [{"name": "Options", "choices": [{"name": "Clean", "idx": 0}]}]}]}, "author": "Spicy2332", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Stealth Finishers (ZKV_Takedowns) - v2.0.4", "version": "2.0.4", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9360, "fileId": 50579, "md5": "d930fd46106625a55505090afc137d3d", "fileSize": 7380, "logicalFilename": "v2.0.4", "updatePolicy": "exact", "tag": "Qpx35-kseBy"}, "hashes": [{"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\ZKVTD\\init.lua", "md5": "58bac25799fbea4579f2b39203020354"}, {"path": "r6\\scripts\\ZKVTakedowns2\\ZKV_StealthFinishers.reds", "md5": "17bc9a42e85b7b6bfe1ebd085caad8db"}], "author": "Kvalyr", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Vehicle Summon Tweaks - <PERSON><PERSON><PERSON>", "version": "2.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4658, "fileId": 50409, "md5": "3591fa54b3168c7dd25014d98ea848af", "fileSize": 1711, "logicalFilename": "Vehicle Summon Tweaks - <PERSON><PERSON><PERSON>", "updatePolicy": "exact", "tag": "ehS4HOLacBt"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Find-EX", "version": "1.2.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 8340, "fileId": 52766, "md5": "3645567dbe5f0b2f939288ba58e55635", "fileSize": 5720, "logicalFilename": "Find-EX", "updatePolicy": "exact", "tag": "2Kzqs9h0XXm"}, "author": "psiberx", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "DLC Liberation Protocol", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 12322, "fileId": 64734, "md5": "c5837a408dde3027ec97b31b21bbdc31", "fileSize": 4251, "logicalFilename": "DLC Liberation Protocol", "updatePolicy": "exact", "tag": "HKLgJdnfezX"}, "author": "<PERSON><PERSON>", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Ragdoll Physics Overhaul 2.0 BETA - Main File", "version": "2.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 3858, "fileId": 65492, "md5": "153ba992f43dac8e3351d49752f0ba69", "fileSize": 49987, "logicalFilename": "Ragdoll Physics Overhaul 2.0 BETA - Main File", "updatePolicy": "exact", "tag": "GfzFdWhBlyH"}, "author": "SketchCritic", "details": {"category": "Miscellaneous", "type": ""}, "phase": 1}, {"name": "All Vehicles Can Steer", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 18210, "fileId": 93731, "md5": "8a6d32a7a0ff8302ed9cec6330a37c42", "fileSize": 1442, "logicalFilename": "All Vehicles Can Steer", "updatePolicy": "exact", "tag": "88RhqNNCUKc"}, "author": "Seijax", "details": {"category": "Vehicles", "type": ""}, "phase": 1}, {"name": "Dodging Fix", "version": "0.11", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 17923, "fileId": 92628, "md5": "adb69fd66d428b7cd94bc8ee784c930d", "fileSize": 1176, "logicalFilename": "Dodging Fix", "updatePolicy": "exact", "tag": "OwdtnO3VwZG"}, "author": "sosuineps<PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Gambling System - Roulette", "version": "1.0.13", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15450, "fileId": 94764, "md5": "6dc8def60bac18586b3b8ed28b072a89", "fileSize": 524790, "logicalFilename": "Gambling System - Roulette", "updatePolicy": "exact", "tag": "eabWaFavwU7"}, "hashes": [{"path": "archive\\pc\\mod\\Gambling System - Roulette.archive", "md5": "4d2dacdeea910e5e9a8f159daea84179"}, {"path": "archive\\pc\\mod\\gambling_system_roulette.xl", "md5": "fc7667f35c324946c3d73a76125ec9f1"}, {"path": "archive\\pc\\mod\\gambling_system_roulette_auto_generated.xl", "md5": "922b2a537a837c85340e9f8a7bf466e4"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\Gambling System - Roulette\\init.lua", "md5": "f57001c17363641af2a3e4514226fe94"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\Gambling System - Roulette\\External\\Cron.lua", "md5": "172f12af8b5a27b6947a30dc7c0684c6"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\Gambling System - Roulette\\External\\GameLocale.lua", "md5": "125a989daa354a792411e67271eb6dc6"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\Gambling System - Roulette\\External\\GameSession.lua", "md5": "80cd27450246ba8e1562c79f2a6a67b3"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\Gambling System - Roulette\\External\\GameUI.lua", "md5": "998f0c340cf320a368cfabd59e465dab"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\Gambling System - Roulette\\External\\interactionUI.lua", "md5": "b2cc5674060c2c249d15e7b5175b3317"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\Gambling System - Roulette\\External\\workspotUtils.lua", "md5": "2a5fa22d3ae616abad75513bd55fd502"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\Gambling System - Roulette\\External\\worldInteraction.lua", "md5": "375cda441b52c9f8e5fd9f52d457e3d5"}, {"path": "bin\\x64\\plugins\\cyber_engine_tweaks\\mods\\Gambling System - Roulette\\lang\\en-us.lua", "md5": "143c01416954a95e1fa2935b57ae58d6"}], "author": "Boe6", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Loot Icons Extension Light", "version": "1.5", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16386, "fileId": 92951, "md5": "ea30c383df5db3e947abf5353379ebe5", "fileSize": 41879, "logicalFilename": "Loot Icons Extension Light", "updatePolicy": "exact", "tag": "8NZcFMDEDJA"}, "hashes": [{"path": "archive\\pc\\mod\\LootIconsExtension.archive.xl", "md5": "52452f83ac18e68206b8fb7d409e3f70"}, {"path": "archive\\pc\\mod\\LootIconsExtensionLight.archive", "md5": "1d236f51640b44280970e052d0b1f87a"}, {"path": "r6\\scripts\\LootIconsExtension\\LootIconsExtension.reds", "md5": "49bf89e0044427b8bd5c75e717c1f18d"}, {"path": "r6\\scripts\\LootIconsExtension\\LootIconsExtension_tints.reds", "md5": "abf5ade1c73beae8260639921d4b7e16"}, {"path": "r6\\scripts\\LootIconsExtension\\LootIconsExtension_yml.reds", "md5": "783395e92601293724468427dc10f684"}], "author": "Demon9ne", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>amp - <PERSON><PERSON><PERSON><PERSON>", "version": "1.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 18208, "fileId": 94291, "md5": "8f6626810ca943b66dc04c80ebd2c7e4", "fileSize": 1168, "logicalFilename": "<PERSON><PERSON><PERSON><PERSON>", "updatePolicy": "exact", "tag": "TIQ0feoPmo2"}, "author": "Seijax", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON><PERSON><PERSON> and Nowaki Revamp - Nowaki", "version": "1.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 18208, "fileId": 94289, "md5": "528fa14433c666cf3718f432930dbd02", "fileSize": 1167, "logicalFilename": "<PERSON><PERSON>", "updatePolicy": "exact", "tag": "eXnkPU43Y-H"}, "author": "Seijax", "details": {"category": "Weapons", "type": ""}, "phase": 1}, {"name": "Set Bonuses", "version": "2.1u3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 8103, "fileId": 93337, "md5": "4ce4edd2af3bc215bd0191a5e18d83d4", "fileSize": 18973, "logicalFilename": "Set Bonuses", "updatePolicy": "exact", "tag": "FyJNGIsQIGj"}, "author": "apart", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Sonic Shock Restored", "version": "1.3.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11438, "fileId": 94672, "md5": "f26934163a478bccacc4c38b309f06d6", "fileSize": 2531, "logicalFilename": "Sonic Shock Restored", "updatePolicy": "exact", "tag": "fo5f_NNVZGY"}, "author": "Seijax", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Throttled Activity Log", "version": "1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 17674, "fileId": 92714, "md5": "985b4e3397acc48520bd65e02ba16212", "fileSize": 1363, "logicalFilename": "Throttled Activity Log", "updatePolicy": "exact", "tag": "9RAgDbK7kCE"}, "hashes": [{"path": "r6\\scripts\\ThrottledActivityLog\\ThrottledActivityLog.reds", "md5": "ba467bd0f322ff006bc53618e38fad5d"}], "author": "Demon9ne", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Apartment Cats Customs - Base files", "version": "1.3.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 6837, "fileId": 102903, "md5": "22468348333598278108211a607c6d2d", "fileSize": 67161798, "logicalFilename": "Apartment Cats Customs - Base files", "updatePolicy": "exact", "tag": "aUJsoccRDy1"}, "author": "Deceptious - xBaebsae", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Immersive Vik", "version": "2.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 6794, "fileId": 53183, "md5": "bcc46fbf57038ecc64c7f3e20e69f710", "fileSize": 34860, "logicalFilename": "Immersive Vik", "updatePolicy": "exact", "tag": "yepQZzE2P50"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Higher TV Quality", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 13090, "fileId": 68826, "md5": "c915c9a5e51a431f13e6be8be028922b", "fileSize": 932, "logicalFilename": "Higher TV Quality", "updatePolicy": "exact", "tag": "MPegzycX6WE"}, "author": "Deceptious", "details": {"category": "Visuals and Graphics", "type": ""}, "phase": 2}, {"name": "Kerry Interactions Enhanced", "version": "2.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4990, "fileId": 49386, "md5": "8050db7da1602c9dd77173fffd9bd59f", "fileSize": 442491, "logicalFilename": "Kerry Interactions Enhanced", "updatePolicy": "exact", "tag": "lA9JF3hY9ll"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Apartment Cats - Dogtown", "version": "1.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10325, "fileId": 72409, "md5": "05098c8b7f1f44c0a21f588eae86dcdf", "fileSize": 82187, "logicalFilename": "Apartment Cats - Dogtown", "updatePolicy": "exact", "tag": "G95Z6Hi6XhI"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "<PERSON><PERSON><PERSON> (Lady Mar<PERSON>)", "version": "1.2.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11097, "fileId": 78574, "md5": "ba6f21d168b116d916e6c9f2dd961165", "fileSize": 3532475, "logicalFilename": "<PERSON><PERSON><PERSON> (Lady Mar<PERSON>)", "updatePolicy": "exact", "tag": "ARNEGYGlkLt"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Californication", "version": "2.3.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7833, "fileId": 78582, "md5": "bffcf07497bfda485285a04a51cbdac9", "fileSize": 39551, "logicalFilename": "Californication", "updatePolicy": "exact", "tag": "Zr3wg2FG6Nn"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Here's <PERSON>", "version": "1.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 13779, "fileId": 78579, "md5": "e170234a721002b7d9ca2402dd3c3d65", "fileSize": 305410, "logicalFilename": "Here's <PERSON>", "updatePolicy": "exact", "tag": "oEoau-9LVUo"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Hot Fuzz", "version": "2.3.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7832, "fileId": 78585, "md5": "a69cc55ac559e0f742c8117cb7094ab4", "fileSize": 81324, "logicalFilename": "Hot Fuzz", "updatePolicy": "exact", "tag": "e6dG6qh_oEn"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Kabuki Gun Range Enhanced", "version": "1.2.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 13466, "fileId": 78581, "md5": "8bde9fa80ceafd169e989aae91603efc", "fileSize": 141431, "logicalFilename": "Kabuki Gun Range Enhanced", "updatePolicy": "exact", "tag": "94Ao3j0DTSE"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "One More Light", "version": "2.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7834, "fileId": 78586, "md5": "3ca7ae6c00c165a47c52772fc216a1d9", "fileSize": 32117, "logicalFilename": "One More Light", "updatePolicy": "exact", "tag": "RnUvy_8WeqZ"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Rolller Coaster Enhanced", "version": "1.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14617, "fileId": 78588, "md5": "6c79462ce4569e9c18de0b9f4830285a", "fileSize": 107559, "logicalFilename": "Rolller Coaster Enhanced", "updatePolicy": "exact", "tag": "g7dz0B2jwtP"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Stencil Text Enhanced", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15154, "fileId": 79521, "md5": "5a988d87bf3c969a3df4d1f902dbf6f8", "fileSize": 74046, "logicalFilename": "Stencil Text Enhanced", "updatePolicy": "exact", "tag": "SLykZyc1Ln1"}, "author": "Deceptious", "details": {"category": "Visuals and Graphics", "type": ""}, "phase": 2}, {"name": "TV Mute Control", "version": "1.1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14149, "fileId": 79003, "md5": "184ff5a4c71c659c7414b76b3d6913bf", "fileSize": 5300, "logicalFilename": "TV Mute Control", "updatePolicy": "exact", "tag": "BQUCSRluyX1"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Encore", "version": "2.3.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 8413, "fileId": 83798, "md5": "05a99b4754e65b10c4af03a260ee9bf4", "fileSize": 57393, "logicalFilename": "Encore", "updatePolicy": "exact", "tag": "AK8nfp1FCml"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Interactive Judys Apartment Devices", "version": "3.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 8099, "fileId": 83793, "md5": "645712fbe8e727c32d8a9a2dc6e9f997", "fileSize": 122481, "logicalFilename": "Interactive Judys Apartment Devices", "updatePolicy": "exact", "tag": "MmTuroWmL7e"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "NCI Addon - Badlands and Pacifica - NCI Beer Tweak", "version": "1.1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15138, "fileId": 81360, "md5": "fc35622217852af8dafe848d57b4ef0a", "fileSize": 701, "logicalFilename": "NCI Beer Tweak", "updatePolicy": "exact", "tag": "nppF6m0OlVP"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "NCI Westbrook", "version": "1.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14805, "fileId": 83781, "md5": "3b4de3e3bacbdac86c385f6bab951345", "fileSize": 2188397, "logicalFilename": "NCI Westbrook", "updatePolicy": "exact", "tag": "ZjkfawsuCAM"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Wilsons Range", "version": "2.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7367, "fileId": 82232, "md5": "91fed6434df3686a505a0efb5ee86b75", "fileSize": 72383, "logicalFilename": "Wilsons Range", "updatePolicy": "exact", "tag": "lQH1hRX1Jc9"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Faster Checkpoints", "version": "1.1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9724, "fileId": 94435, "md5": "fab0e4a63be65a4d51f2007338562f9d", "fileSize": 40995, "logicalFilename": "Faster Checkpoints", "updatePolicy": "exact", "tag": "vZomL8-DJCl"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "NCI Watson", "version": "1.3.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14804, "fileId": 94510, "md5": "7928202c4544ef0fb623cfb630438541", "fileSize": 2282621, "logicalFilename": "NCI Watson", "updatePolicy": "exact", "tag": "j3lB5s4trWW"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Dance Off", "version": "1.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10615, "fileId": 95735, "md5": "6de251eccc9233269e6213058ec8052c", "fileSize": 134847, "logicalFilename": "Dance Off", "updatePolicy": "exact", "tag": "DF9HTP_HtJQ"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "The Passenger Settings", "version": "1.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 18380, "fileId": 95486, "md5": "3b8031c2f800afc0f74dbd5eff7c7999", "fileSize": 48070, "logicalFilename": "The Passenger Settings", "updatePolicy": "exact", "tag": "jJ8D18kvdrB"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Immersive Rippers - Dogtown", "version": "1.2.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10255, "fileId": 99143, "md5": "ea0601cfc203b8b36962d719bd85cdef", "fileSize": 103841, "logicalFilename": "Immersive Rippers - Dogtown", "updatePolicy": "exact", "tag": "ZkeZRRucFaS"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Parking Spots Enhanced", "version": "1.2.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15708, "fileId": 99146, "md5": "3a0f731e1332c04688e679a561aeb0a9", "fileSize": 341217, "logicalFilename": "Parking Spots Enhanced", "updatePolicy": "exact", "tag": "ctpzqD5t0b2"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Apartment Cats - Corpo Plaza", "version": "2.2.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 6329, "fileId": 102913, "md5": "186e089915044b1f3a4f7fdbe8e87097", "fileSize": 12045111, "logicalFilename": "Apartment Cats - Corpo Plaza", "updatePolicy": "exact", "tag": "IZn0A53AnXe"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Apartment Cats - Japantown", "version": "2.2.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 6493, "fileId": 102912, "md5": "6640e9644ec7b1ac5d9b7161829a9fae", "fileSize": 88832, "logicalFilename": "Apartment Cats - Japantown", "updatePolicy": "exact", "tag": "5FrmRi-vJL3"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Apartment Cats - Northside Motel", "version": "2.2.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 6379, "fileId": 102911, "md5": "0ab871ea635c4993b9c6feaf90023b41", "fileSize": 64855, "logicalFilename": "Apartment Cats - Northside Motel", "updatePolicy": "exact", "tag": "8Qsv2supnHi"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Apartment Cats - The Glen", "version": "2.1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 6276, "fileId": 102910, "md5": "aa80ea7e168f8b7145704a77ce8cacc8", "fileSize": 73335, "logicalFilename": "Apartment Cats - The Glen", "updatePolicy": "exact", "tag": "xW1ckk-CPV3"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Flaming <PERSON>rot<PERSON> Man Romanced", "version": "1.3.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9573, "fileId": 105390, "md5": "de53f9d9dd8c9ccadddccefdc2185e75", "fileSize": 79224, "logicalFilename": "Flaming <PERSON>rot<PERSON> Man Romanced", "updatePolicy": "exact", "tag": "mdzz28_6jgp"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Gone Away - Better Partner Suspended <PERSON><PERSON><PERSON> - Gone Away", "version": "0.9.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19412, "fileId": 99822, "md5": "ff2410380f95ee7eb42aad20d2d6e2eb", "fileSize": 179522, "logicalFilename": "Gone Away", "updatePolicy": "exact", "tag": "zw5gjRRt_Gs"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Immersive Bartenders", "version": "2.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7203, "fileId": 105031, "md5": "307d4af3b6d719edccce619c45885fe6", "fileSize": 670682, "logicalFilename": "Immersive Bartenders", "updatePolicy": "exact", "tag": "G79I9QxnTaA"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Immersive Bartenders - Dogtown", "version": "2.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10372, "fileId": 105032, "md5": "134b336262c24e5d4d895250799d7ab2", "fileSize": 242379, "logicalFilename": "Immersive Bartenders - Dogtown", "updatePolicy": "exact", "tag": "ZqGv2e1nVEI"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Immersive Food Vendors", "version": "1.1.1h", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7322, "fileId": 103572, "md5": "55a3d27a8fe4d9c79e8371fbdb966516", "fileSize": 396833, "logicalFilename": "Immersive Food Vendors", "updatePolicy": "exact", "tag": "nIxYXp9xAHf"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Immersive Food Vendors - Dogtown", "version": "1.0.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10393, "fileId": 104674, "md5": "e1a54885281c35df98f080dedce81bd0", "fileSize": 103041, "logicalFilename": "Immersive Food Vendors - Dogtown", "updatePolicy": "exact", "tag": "i6I7KREg0pt"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "<PERSON>", "version": "2.3.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4508, "fileId": 102837, "md5": "4ae0bd6db13cdbea7d86896232175dd5", "fileSize": 103448, "logicalFilename": "<PERSON>", "updatePolicy": "exact", "tag": "w14pmTWsimr"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "NCI City Center", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 21018, "fileId": 106784, "md5": "6b6bdb5a366e26d084fc9b0d06f4e119", "fileSize": 683916, "logicalFilename": "NCI City Center", "updatePolicy": "exact", "tag": "0Kr4hoExyF1"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "NCI Santo Domingo", "version": "1.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19005, "fileId": 106728, "md5": "df5ea15e28e8c858d47443518257db10", "fileSize": 1940471, "logicalFilename": "NCI Santo Domingo", "updatePolicy": "exact", "tag": "mvusT0a6VxD"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Panam Romanced Enhanced", "version": "2.4.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4626, "fileId": 99818, "md5": "47cba5544b8e70c06ab6beb42ef1c130", "fileSize": 236746, "logicalFilename": "Panam Romanced Enhanced", "updatePolicy": "exact", "tag": "1uxVKZ6P68i"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Pet Your Cat", "version": "2.3.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 6198, "fileId": 102909, "md5": "3bb5d1512e44f62c501496ff67f0ae3b", "fileSize": 111739, "logicalFilename": "Pet Your Cat", "updatePolicy": "exact", "tag": "CYrfs0NdpHm"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Really Want To Stay At Your House - <PERSON>", "version": "3.4.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 8753, "fileId": 102915, "md5": "aaef791273c2536184e50c933d38dbf0", "fileSize": 528507, "logicalFilename": "Really Want To Stay At Your House - <PERSON>", "updatePolicy": "exact", "tag": "J1fxrMqfUc4"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Really Want To Stay At Your House - <PERSON>", "version": "3.3.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 8806, "fileId": 102916, "md5": "c7c605de28739f99d0e18d15a01dd343", "fileSize": 464955, "logicalFilename": "Really Want To Stay At Your House - <PERSON>", "updatePolicy": "exact", "tag": "33FozOQ68RT"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Really Want To Stay At Your House - Panam", "version": "3.3.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 8775, "fileId": 102917, "md5": "c54122bd9abaf7da7c1deb8634bd0fef", "fileSize": 415543, "logicalFilename": "Really Want To Stay At Your House - Panam", "updatePolicy": "exact", "tag": "4FMten2Thyh"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Really Want To Stay At Your House - River", "version": "3.3.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 8826, "fileId": 102918, "md5": "7c21713c72100eb49eb6034495bfb586", "fileSize": 408111, "logicalFilename": "Really Want To Stay At Your House - River", "updatePolicy": "exact", "tag": "wgpg9Lpeak_"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Romance Hangouts Enhanced", "version": "2.1.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11590, "fileId": 102914, "md5": "6aa6889cab7be58da94685fa7029e6f1", "fileSize": 8802307, "logicalFilename": "Romance Hangouts Enhanced", "updatePolicy": "exact", "tag": "aCOR4sUrcnx"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "WTNC Config", "version": "1.9.5", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 10426, "fileId": 108005, "md5": "fda54d281a6b1f8686ad7bf436ae7ed3", "fileSize": 9204987, "logicalFilename": "WTNC Config", "updatePolicy": "exact", "tag": "N4nup08oogj"}, "author": "Cyberpunk THING Team", "details": {"category": "Miscellaneous", "type": ""}, "phase": 3}, {"name": "Custom Quickslots (Consumables Grenades and Cyberware Abilities) - Custom Quickslots", "version": "5.1.6", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 3096, "fileId": 108042, "md5": "5c7d26158e13bd28c2655f85abe6ba31", "fileSize": 50511, "logicalFilename": "Custom Quickslots", "updatePolicy": "exact", "tag": "w40pNMGJ7SD"}, "author": "RMK", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Enhanced Craft", "version": "4.0.5", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4378, "fileId": 109783, "md5": "639d00c4451bee55d6c78e9c935f1f5a", "fileSize": 91019, "logicalFilename": "Enhanced Craft", "updatePolicy": "exact", "tag": "biLjiPBFuEk"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Crafting", "type": ""}, "phase": 1}, {"name": "Hold to Overclock while scanning", "version": "1.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 21656, "fileId": 109982, "md5": "2ac062e8937f1635c19b6e6e66cbb1ed", "fileSize": 1944, "logicalFilename": "Hold to Overclock while scanning", "updatePolicy": "exact", "tag": "8UsluGFanxT"}, "author": "The guy posting it", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Immersive Timeskip", "version": "2.1.4", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 5115, "fileId": 109784, "md5": "b2d2ea2bded0b923cd7be2eba1c181b4", "fileSize": 11531, "logicalFilename": "Immersive Timeskip", "updatePolicy": "exact", "tag": "aIb9i2t7yWQ"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Judy Identity Privacy", "version": "1.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 17486, "fileId": 108157, "md5": "a6ed20fcf3d17b73093065a9265586fb", "fileSize": 46827, "logicalFilename": "Judy Identity Privacy", "updatePolicy": "exact", "tag": "P1wUCX9Xhut"}, "author": "fishnipples129 or Dane", "details": {"category": "Audio", "type": ""}, "phase": 1}, {"name": "Metro Pocket Guide", "version": "1.2.8", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 11882, "fileId": 109788, "md5": "93340adfc4e1ab4df469ff1528156d9f", "fileSize": 120800, "logicalFilename": "Metro Pocket Guide", "updatePolicy": "exact", "tag": "bQTJ3ScIip0"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "<PERSON><PERSON><PERSON> horn fix", "version": "1.01", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 21518, "fileId": 109102, "md5": "ab1ecd0afb4fc8847cc13cf9232ca65d", "fileSize": 701, "logicalFilename": "<PERSON><PERSON><PERSON> horn fix", "updatePolicy": "exact", "tag": "Am2rfFKoaNJ"}, "author": "neburas", "details": {"category": "Audio", "type": ""}, "phase": 1}, {"name": "MovementAndCameraTweaks", "version": "1.4", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4039, "fileId": 108568, "md5": "f1afbdddc7179f7f85e17fb4f1ddba9d", "fileSize": 2758, "logicalFilename": "MovementAndCameraTweaks", "updatePolicy": "exact", "tag": "eTrNb0JCS12"}, "author": "sosuineps<PERSON><PERSON><PERSON>", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Muted Markers", "version": "2.3.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 1727, "fileId": 109789, "md5": "a0c90607ce48046dc43aa671825fd396", "fileSize": 23200, "logicalFilename": "Muted Markers", "updatePolicy": "exact", "tag": "WRJ7RuIxYnI"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Native Settings UI Side Menu Add-on 1.3.8", "version": "1.3.8", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 16218, "fileId": 108126, "md5": "8f2ca291f39d8a1d6a5e4771713215f4", "fileSize": 34327, "logicalFilename": "Native Settings UI Side Menu Add-on 1.3.8", "updatePolicy": "exact", "tag": "96gMarSX6-X"}, "author": "anygoodname", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Neuralware - Chipware Expansion", "version": "1.1.9", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19798, "fileId": 109474, "md5": "1740ddc98975143c64914f680b8814cb", "fileSize": 1816290, "logicalFilename": "Neuralware - Chipware Expansion", "updatePolicy": "exact", "tag": "KRU18MrPcQj"}, "author": "Phoenicia", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Night City Interactions - Core", "version": "3.5.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 5519, "fileId": 109545, "md5": "6b7905c1ff44a9a86631f92753f8eb37", "fileSize": 26023766, "logicalFilename": "Night City Interactions - Core", "updatePolicy": "exact", "tag": "Ne-M8djrk7p"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Psycho Killer Reward - Restored - Core Files", "version": "1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15497, "fileId": 107930, "md5": "c40e48d60456853dbebec972bf0d31d4", "fileSize": 514, "logicalFilename": "Core Files", "updatePolicy": "exact", "tag": "FEBCuQGzjq-"}, "author": "saltypigloaf", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Quickhack Fixes", "version": "1.2.16", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 18290, "fileId": 109718, "md5": "bd47aa02dcdb0a4440e13e86ccf15f74", "fileSize": 25527, "logicalFilename": "Quickhack Fixes", "updatePolicy": "exact", "tag": "4FMa5TujsHZ"}, "author": "The guy posting it", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Revised Backpack", "version": "0.9.9", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 17642, "fileId": 109791, "md5": "1fe271a187ce2b652a4d24c9ba87538f", "fileSize": 68190, "logicalFilename": "Revised Backpack", "updatePolicy": "exact", "tag": "BFpYynxwgOc"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "details": {"category": "User Interface", "type": ""}, "phase": 1}, {"name": "Stealthrunner - Stealth Gameplay Expansion - Stealthrunner", "version": "1.8.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7616, "fileId": 108487, "md5": "be78d20fb6715ed60223e2cd173e474a", "fileSize": 37957181, "logicalFilename": "Stealthrunner", "updatePolicy": "exact", "tag": "hbvzsnJROUX"}, "author": "RMK", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Trigger Mode Control", "version": "2.7.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 13077, "fileId": 108239, "md5": "cda8fbe035a2bb6599da54948fda0ddc", "fileSize": 12570, "logicalFilename": "Trigger Mode Control", "updatePolicy": "exact", "tag": "wfT3Ds688_N"}, "author": "Seijax", "details": {"category": "Modders Resources", "type": ""}, "phase": 1}, {"name": "TwinTone Stores - with items and customs support - TwinTone Stores", "version": "1.0.3", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 19992, "fileId": 108330, "md5": "acb2c82ab399ff3c4e655c32cdd016cb", "fileSize": 521092, "logicalFilename": "TwinTone Stores", "updatePolicy": "exact", "tag": "cWwmP_iMUGe"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Untrack Quest Ultimate 3.1.0", "version": "3.1.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 6328, "fileId": 108694, "md5": "8d95f8071b640dda9195d64a2ea7114a", "fileSize": 33641, "logicalFilename": "Untrack Quest Ultimate 3.1.0", "updatePolicy": "exact", "tag": "_M029XEgdyH"}, "author": "anygoodname", "details": {"category": "Gameplay", "type": ""}, "phase": 1}, {"name": "Courier Job Settings", "version": "1.0.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 21707, "fileId": 109895, "md5": "7c879c4bf17a4432d4fdbce54133e747", "fileSize": 146643, "logicalFilename": "Courier Job Settings", "updatePolicy": "exact", "tag": "V7VIvoJ3UUp"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Deceptious Bug Fixes", "version": "1.1.11", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 18318, "fileId": 108722, "md5": "91526f8a5b4826564753898fe0171156", "fileSize": 2085769, "logicalFilename": "Deceptious Bug Fixes", "updatePolicy": "exact", "tag": "og28QDM44BD"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Immersive Rippers", "version": "2.4.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 7064, "fileId": 109547, "md5": "bcdd8dc1b326e615b4a7b7e15579e0d1", "fileSize": 461959, "logicalFilename": "Immersive Rippers", "updatePolicy": "exact", "tag": "O46n-uuR5sD"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "Maelstrom Crystal Coat", "version": "1.0.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 21351, "fileId": 108486, "md5": "e34324a1d9718a1587046f532dbd9e97", "fileSize": 675689, "logicalFilename": "Maelstrom Crystal Coat", "updatePolicy": "exact", "tag": "cFg3w9pP1jJ"}, "author": "Deceptious", "details": {"category": "Appearance", "type": ""}, "phase": 2}, {"name": "Matrix Crystal Coat", "version": "1.0.2", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 21319, "fileId": 108271, "md5": "23917550466ca5b813ca909e927a916f", "fileSize": 1391663, "logicalFilename": "Matrix Crystal Coat", "updatePolicy": "exact", "tag": "mUjgsTftVGC"}, "author": "Deceptious", "details": {"category": "Appearance", "type": ""}, "phase": 2}, {"name": "NCI Badlands and Pacifica", "version": "1.4.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 15138, "fileId": 109543, "md5": "d0382fe85689ce38d387b06dcfcd5e33", "fileSize": 4766091, "logicalFilename": "NCI Badlands and Pacifica", "updatePolicy": "exact", "tag": "Mww2Lks31sx"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "NCI Heywood", "version": "1.3.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 14806, "fileId": 109544, "md5": "eca9f7dcab9c905675bf27e3302e4c6c", "fileSize": 1774940, "logicalFilename": "NCI Heywood", "updatePolicy": "exact", "tag": "mx8py0Qt-e3"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "<PERSON>", "version": "2.4.0", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 9191, "fileId": 108870, "md5": "5ff8d219c1de3f1394ec1d3065105377", "fileSize": 1426108, "logicalFilename": "<PERSON>", "updatePolicy": "exact", "tag": "MK4B_hoCcIM"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}, {"name": "River Romanced Enhanced", "version": "2.3.1", "optional": false, "domainName": "cyberpunk2077", "source": {"type": "nexus", "modId": 4870, "fileId": 108572, "md5": "d5a78eb3ae4b4b2e241b0c85b5d25137", "fileSize": 137600, "logicalFilename": "River Romanced Enhanced", "updatePolicy": "exact", "tag": "OIsl7oVvGAl6"}, "author": "Deceptious", "details": {"category": "Gameplay", "type": ""}, "phase": 2}], "modRules": [{"type": "after", "reference": {"fileExpression": "DR-10 Wormhole-10541-1-0-2-1701690832", "fileMD5": "42e99607eeec62835674c07333708fdb", "versionMatch": "1.0.2", "logicalFileName": "DR-10 <PERSON><PERSON><PERSON>"}, "source": {"fileExpression": "WTNC Config-10426-1-9-5-1746099612", "fileMD5": "fda54d281a6b1f8686ad7bf436ae7ed3", "versionMatch": "1.9.5", "logicalFileName": "WTNC Config"}}, {"type": "after", "reference": {"fileExpression": "GLEIPNIR-12695-1-0-0-1706806049", "fileMD5": "9fbf72f529f07c4c355536ba59b45425", "versionMatch": "1.0.0", "logicalFileName": "GLEIPNIR"}, "source": {"fileExpression": "WTNC Config-10426-1-9-5-1746099612", "fileMD5": "fda54d281a6b1f8686ad7bf436ae7ed3", "versionMatch": "1.9.5", "logicalFileName": "WTNC Config"}}, {"type": "after", "reference": {"fileExpression": "Ricochet Redux-7197-4-1-1-1738516115", "fileMD5": "5c3f430ff175321d0ac80fd275880218", "versionMatch": "4.1.1", "logicalFileName": "<PERSON><PERSON> Redux"}, "source": {"fileExpression": "WTNC Config-10426-1-9-5-1746099612", "fileMD5": "fda54d281a6b1f8686ad7bf436ae7ed3", "versionMatch": "1.9.5", "logicalFileName": "WTNC Config"}}, {"type": "after", "reference": {"fileExpression": "Borg Mal<PERSON>n-9213-1-6-0-1729704968", "fileMD5": "86e12bb5ce72a918a129e9d99d78aac1", "versionMatch": "1.6.0", "logicalFileName": "<PERSON><PERSON>"}, "source": {"fileExpression": "WTNC Config-10426-1-9-5-1746099612", "fileMD5": "fda54d281a6b1f8686ad7bf436ae7ed3", "versionMatch": "1.9.5", "logicalFileName": "WTNC Config"}}, {"type": "after", "reference": {"fileExpression": "React To Horn-16965-1-2-0-1729198584", "fileMD5": "196a95db3add9bd1e6df50c45e131671", "versionMatch": "1.2.0", "logicalFileName": "React To <PERSON>"}, "source": {"fileExpression": "WTNC Config-10426-1-9-5-1746099612", "fileMD5": "fda54d281a6b1f8686ad7bf436ae7ed3", "versionMatch": "1.9.5", "logicalFileName": "WTNC Config"}}, {"type": "after", "reference": {"fileExpression": "SynthDose-14094-1-3-9-1742372719", "fileMD5": "f21a3f4175049ee669bb42fed2cf69b6", "versionMatch": "1.3.9", "logicalFileName": "SynthDose"}, "source": {"fileExpression": "WTNC Config-10426-1-9-5-1746099612", "fileMD5": "fda54d281a6b1f8686ad7bf436ae7ed3", "versionMatch": "1.9.5", "logicalFileName": "WTNC Config"}}], "loadOrder": [], "tools": [], "collectionConfig": {"recommendNewProfile": false}}