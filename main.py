import json
import sys
import os
import time
import requests
import re
import gzip
import io
import random
import threading
import logging
from time import sleep
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import brotli
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from tqdm import tqdm
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn, TimeRemainingColumn
from rich.live import Live

def setup_output_directory():
    """Create output directory for generated files."""
    output_dir = CONFIG['OUTPUT_DIR']
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"✓ Created output directory: {output_dir}")
    return output_dir

def setup_logging(output_dir):
    """Setup logging with output directory."""
    log_file = os.path.join(output_dir, 'nexusmods_downloader.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

# Initialize logger (will be properly set up in main())
logger = logging.getLogger(__name__)

# Global configuration

CONFIG = {
    'REQUEST_TIMEOUT': 30,
    'MAX_RETRIES': 3,
    'RETRY_BACKOFF': 2,
    'MIN_DELAY': 5,
    'MAX_DELAY': 15,
    'RATE_LIMIT_DELAY': 60,
    'MAX_CONCURRENT_REQUESTS': 1,  # Conservative for anti-bot
    'OUTPUT_DIR': 'nexusmods_output',
}

# Supported games configuration
SUPPORTED_GAMES = {
    'cyberpunk2077': {
        'name': 'Cyberpunk 2077',
        'url_path': 'cyberpunk2077',
        'icon': '🌃',
        'color': 'cyan'
    },
    'skyrimspecialedition': {
        'name': 'The Elder Scrolls V: Skyrim Special Edition',
        'url_path': 'skyrimspecialedition',
        'icon': '🏔️',
        'color': 'blue'
    },
    'fallout4': {
        'name': 'Fallout 4',
        'url_path': 'fallout4',
        'icon': '☢️',
        'color': 'yellow'
    },
    'witcher3': {
        'name': 'The Witcher 3: Wild Hunt',
        'url_path': 'witcher3',
        'icon': '🐺',
        'color': 'red'
    },
    'skyrim': {
        'name': 'The Elder Scrolls V: Skyrim',
        'url_path': 'skyrim',
        'icon': '🏔️',
        'color': 'blue'
    },
    'fallout76': {
        'name': 'Fallout 76',
        'url_path': 'fallout76',
        'icon': '☢️',
        'color': 'green'
    },
    'newvegas': {
        'name': 'Fallout: New Vegas',
        'url_path': 'newvegas',
        'icon': '🎰',
        'color': 'orange'
    },
    'oblivion': {
        'name': 'The Elder Scrolls IV: Oblivion',
        'url_path': 'oblivion',
        'icon': '🏛️',
        'color': 'purple'
    },
    'morrowind': {
        'name': 'The Elder Scrolls III: Morrowind',
        'url_path': 'morrowind',
        'icon': '🌋',
        'color': 'magenta'
    },
    'stardewvalley': {
        'name': 'Stardew Valley',
        'url_path': 'stardewvalley',
        'icon': '🚜',
        'color': 'green'
    }
}

# Initialize rich console
console = Console()

# Browser session configuration - simulates ONE consistent browser
class BrowserSession:
    """Simulates a consistent browser session with realistic fingerprinting."""

    def __init__(self):
        # Pick ONE browser configuration and stick with it for the entire session
        self.browser_type = random.choice(['chrome_windows', 'firefox_windows', 'edge_windows'])
        self.session_id = random.randint(100000, 999999)

        # Generate consistent browser fingerprint
        if self.browser_type == 'chrome_windows':
            chrome_version = random.choice(['120.0.0.0', '119.0.0.0', '121.0.0.0'])
            self.user_agent = f'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{chrome_version} Safari/537.36'
            self.sec_ch_ua = f'"Not_A Brand";v="8", "Chromium";v="{chrome_version.split(".")[0]}", "Google Chrome";v="{chrome_version.split(".")[0]}"'
            self.sec_ch_ua_platform = '"Windows"'
            self.accept = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8'
        elif self.browser_type == 'firefox_windows':
            firefox_version = random.choice(['121.0', '120.0', '119.0'])
            self.user_agent = f'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/{firefox_version}'
            self.sec_ch_ua = None  # Firefox doesn't send these headers
            self.sec_ch_ua_platform = None
            self.accept = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8'
        else:  # edge_windows
            edge_version = random.choice(['120.0.0.0', '119.0.0.0'])
            self.user_agent = f'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{edge_version} Safari/537.36 Edg/{edge_version}'
            self.sec_ch_ua = f'"Not_A Brand";v="8", "Chromium";v="{edge_version.split(".")[0]}", "Microsoft Edge";v="{edge_version.split(".")[0]}"'
            self.sec_ch_ua_platform = '"Windows"'
            self.accept = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8'

        # Consistent language preference
        self.accept_language = random.choice([
            'en-US,en;q=0.9',
            'en-US,en;q=0.8',
            'en-GB,en;q=0.9',
            'en-US,en;q=0.9,es;q=0.8'
        ])

        # Screen resolution (affects some fingerprinting)
        self.screen_resolution = random.choice([
            '1920x1080', '1366x768', '1440x900', '1536x864', '1600x900'
        ])

        logger.info(f"Browser session initialized: {self.browser_type} (Session ID: {self.session_id})")

    def get_headers(self, referer=None):
        """Get consistent headers for this browser session."""
        headers = {
            'User-Agent': self.user_agent,
            'Accept': self.accept,
            'Accept-Language': self.accept_language,
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin' if referer else 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        }

        # Add browser-specific headers
        if self.sec_ch_ua:
            headers['Sec-CH-UA'] = self.sec_ch_ua
            headers['Sec-CH-UA-Mobile'] = '?0'
            headers['Sec-CH-UA-Platform'] = self.sec_ch_ua_platform

        if referer:
            headers['Referer'] = referer

        return headers

class RateLimiter:
    """Rate limiter to prevent overwhelming the server."""
    def __init__(self, min_delay=5, max_delay=15):
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.last_request = 0
        self.request_count = 0
        self.lock = threading.Lock()

    def wait(self):
        """Wait appropriate time before next request."""
        with self.lock:
            current_time = time.time()
            time_since_last = current_time - self.last_request

            # Add randomized delay to appear more human
            base_delay = random.uniform(self.min_delay, self.max_delay)

            # Increase delay if making many requests
            if self.request_count > 10:
                base_delay *= 1.5
            if self.request_count > 20:
                base_delay *= 2

            if time_since_last < base_delay:
                sleep_time = base_delay - time_since_last
                logger.info(f"Rate limiting: waiting {sleep_time:.1f} seconds")
                time.sleep(sleep_time)

            self.last_request = time.time()
            self.request_count += 1

def detect_game_from_collection(json_file_path):
    """
    Detect the game from the collection.json file by reading the domainName.

    Args:
        json_file_path (str): Path to the collection.json file

    Returns:
        dict: Game information or None if not supported
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)

        domain_name = data.get('domainName', '').lower()

        if domain_name in SUPPORTED_GAMES:
            game_info = SUPPORTED_GAMES[domain_name].copy()
            game_info['domain'] = domain_name
            return game_info
        else:
            # Try to find a close match
            for game_key, game_data in SUPPORTED_GAMES.items():
                if game_key in domain_name or domain_name in game_key:
                    game_info = game_data.copy()
                    game_info['domain'] = domain_name
                    console.print(f"[yellow]⚠ Detected similar game: {game_data['name']} (domain: {domain_name})[/yellow]")
                    return game_info

            # Unsupported game
            console.print(f"[red]✗ Unsupported game domain: {domain_name}[/red]")
            console.print(f"[yellow]Supported games:[/yellow]")
            for game_key, game_data in SUPPORTED_GAMES.items():
                console.print(f"  {game_data['icon']} {game_data['name']} ({game_key})")
            return None

    except Exception as e:
        logger.error(f"Error detecting game from collection: {e}")
        return None

def extract_mod_info(json_file_path, session, start_from=1, delay=10, preview_only=False, rate_limiter=None, browser_session=None, output_dir=None, game_info=None):
    """
    Read the JSON file and extract modId and fileId from each mod in the mods array.

    Args:
        json_file_path (str): Path to the collection.json file
        session (requests.Session): Authenticated requests session with cookies
        start_from (int): Mod number to start from (1-based)
        delay (int): Delay in seconds between mod downloads
        preview_only (bool): If True, only show mod info without downloading
        rate_limiter (RateLimiter): Rate limiter instance for request throttling
        browser_session (BrowserSession): Consistent browser session for headers
        output_dir (str): Directory for output files
        game_info (dict): Game information detected from collection
    """
    if rate_limiter is None:
        rate_limiter = RateLimiter(CONFIG['MIN_DELAY'], CONFIG['MAX_DELAY'])
    if browser_session is None:
        browser_session = BrowserSession()
    if output_dir is None:
        output_dir = CONFIG['OUTPUT_DIR']
    if game_info is None:
        game_info = detect_game_from_collection(json_file_path)
        if not game_info:
            return []
    try:
        # Read the JSON file
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        
        # Check if 'mods' key exists
        if 'mods' not in data:
            print("Error: 'mods' key not found in JSON file")
            return
        
        mods = data['mods']
        total_mods = len(mods)
        
        # Validate start_from parameter
        if start_from < 1 or start_from > total_mods:
            print(f"Error: start_from must be between 1 and {total_mods}")
            return
        
        # Display game and collection info with rich formatting
        game_icon = game_info.get('icon', '🎮')
        game_name = game_info.get('name', 'Unknown Game')
        game_color = game_info.get('color', 'white')

        console.print(f"\n{game_icon} [bold {game_color}]{game_name}[/bold {game_color}] Collection")
        console.print(f"Found [bold cyan]{total_mods}[/bold cyan] mods in the collection")

        if start_from > 1:
            console.print(f"Starting from mod [bold yellow]#{start_from}[/bold yellow] (skipping {start_from - 1} mods)")

        mode_color = "yellow" if preview_only else "green"
        mode_text = "Preview only" if preview_only else "Download"
        console.print(f"Mode: [bold {mode_color}]{mode_text}[/bold {mode_color}]")

        # Show collection info if available
        if 'info' in data:
            info = data['info']
            collection_name = info.get('name', 'Unknown Collection')
            author = info.get('author', 'Unknown Author')

            # Create a nice info panel
            info_table = Table.grid(padding=1)
            info_table.add_column(style="bold blue")
            info_table.add_column()
            info_table.add_row("Collection:", f"[bold white]{collection_name}[/bold white]")
            info_table.add_row("Author:", f"[bold white]{author}[/bold white]")
            info_table.add_row("Game:", f"[bold {game_color}]{game_name}[/bold {game_color}]")
            info_table.add_row("Total Mods:", f"[bold cyan]{total_mods}[/bold cyan]")

            console.print(Panel(info_table, title="📋 Collection Information", border_style="blue"))

        console.print()
        
        processed_count = 0
        skipped_count = start_from - 1
        failed_count = 0
        success_count = 0
        failed_mods = []
        start_time = time.time()

        # Create progress tracking in output directory
        progress_file = os.path.join(output_dir, f"progress_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

        # Create progress bar with rich
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            TimeRemainingColumn(),
            console=console,
            transient=False
        ) as progress:

            # Create main progress task
            main_task = progress.add_task(
                f"[{game_color}]Processing {game_icon} {game_name} mods[/{game_color}]",
                total=total_mods - skipped_count
            )

            try:
                # Iterate through each mod starting from the specified position
                for i, mod in enumerate(mods, 1):
                    if i < start_from:
                        continue

                    mod_name = mod.get('name', 'Unknown')
                    mod_version = mod.get('version', 'Unknown')
                    mod_author = mod.get('author', 'Unknown')

                    # Update progress description
                    progress.update(main_task, description=f"[{game_color}]{mod_name[:40]}{'...' if len(mod_name) > 40 else ''}[/{game_color}]")

                    # Save progress
                    save_progress(progress_file, i, total_mods, processed_count, success_count, failed_count)

                    # Check if source exists and has the required fields
                    if 'source' in mod:
                        source = mod['source']
                        mod_id = source.get('modId', 'N/A')
                        file_id = source.get('fileId', 'N/A')

                        # Log mod info
                        console.print(f"[dim]{i:3d}.[/dim] [bold]{mod_name}[/bold] [dim]v{mod_version}[/dim]")
                        console.print(f"     [dim]Author:[/dim] {mod_author}")
                        console.print(f"     [dim]modId:[/dim] {mod_id} [dim]fileId:[/dim] {file_id}")

                        if not preview_only and mod_id != 'N/A' and file_id != 'N/A':
                            # Apply rate limiting
                            rate_limiter.wait()

                            console.print(f"     [yellow]Fetching download page...[/yellow]")
                            download_url = fetch_download_url(session, mod_id, file_id, rate_limiter, browser_session, output_dir, game_info)

                            if download_url:
                                console.print(f"     [green]✓ Download URL found[/green]")
                                try:
                                    if sys.platform.startswith('win'):
                                        os.startfile(download_url)
                                    elif sys.platform.startswith('darwin'):  # macOS
                                        os.system(f'open "{download_url}"')
                                    else:  # Linux
                                        os.system(f'xdg-open "{download_url}"')
                                    console.print(f"     [green]✓ Download link opened successfully[/green]")
                                    success_count += 1
                                    logger.info(f"Successfully processed mod {mod_id}: {mod_name}")
                                except Exception as e:
                                    console.print(f"     [red]✗ Error opening download link: {e}[/red]")
                                    failed_count += 1
                                    failed_mods.append({
                                        'mod_id': mod_id,
                                        'file_id': file_id,
                                        'name': mod_name,
                                        'version': mod_version,
                                        'author': mod_author,
                                        'reason': f'Error opening download link: {e}'
                                    })
                                    logger.error(f"Failed to open download link for mod {mod_id}: {e}")
                            else:
                                console.print(f"     [red]✗ Could not extract download URL[/red]")
                                failed_count += 1
                                failed_mods.append({
                                    'mod_id': mod_id,
                                    'file_id': file_id,
                                    'name': mod_name,
                                    'version': mod_version,
                                    'author': mod_author,
                                    'reason': 'Could not extract download URL'
                                })
                                logger.warning(f"Could not extract download URL for mod {mod_id}: {mod_name}")

                            processed_count += 1

                            # Adaptive delay based on success rate
                            if processed_count > 5:
                                success_rate = success_count / processed_count
                                if success_rate < 0.5:  # Less than 50% success
                                    adaptive_delay = delay * 2
                                    console.print(f"     [yellow]Low success rate detected, increasing delay to {adaptive_delay}s[/yellow]")
                                    time.sleep(adaptive_delay)
                                elif delay > 0:
                                    time.sleep(delay)
                            elif delay > 0:
                                time.sleep(delay)
                        else:
                            status_msg = 'Preview mode - not downloading' if preview_only else 'Skipped (missing mod/file ID)'
                            console.print(f"     [dim]{status_msg}[/dim]")

                        console.print()
                    else:
                        console.print(f"[dim]{i:3d}.[/dim] [bold]{mod_name}[/bold] [dim]v{mod_version}[/dim]")
                        console.print(f"     [dim]Author:[/dim] {mod_author}")
                        console.print(f"     [red]No source data available[/red]")
                        console.print()

                    # Update progress
                    progress.update(main_task, advance=1)

        except KeyboardInterrupt:
            print("\n⚠ Process interrupted by user")
            logger.info("Process interrupted by user")
        except Exception as e:
            print(f"\n✗ Unexpected error: {e}")
            logger.error(f"Unexpected error during processing: {e}")
        finally:
            # Clean up progress file
            try:
                os.remove(progress_file)
            except:
                pass
        
        # Enhanced Summary with Rich formatting
        total_time = time.time() - start_time

        # Create summary table
        summary_table = Table(title=f"📊 Final Summary - {game_icon} {game_name}", show_header=True, header_style="bold magenta")
        summary_table.add_column("Metric", style="bold blue")
        summary_table.add_column("Value", style="bold white")

        summary_table.add_row("Total mods in collection", f"[cyan]{total_mods}[/cyan]")
        summary_table.add_row("Mods skipped (start_from)", f"[yellow]{skipped_count}[/yellow]")

        if not preview_only:
            summary_table.add_row("Mods processed", f"[blue]{processed_count}[/blue]")
            summary_table.add_row("Successful downloads", f"[green]{success_count}[/green]")
            summary_table.add_row("Failed downloads", f"[red]{failed_count}[/red]")

            if processed_count > 0:
                success_rate = (success_count / processed_count) * 100
                rate_color = "green" if success_rate >= 90 else "yellow" if success_rate >= 70 else "red"
                summary_table.add_row("Success rate", f"[{rate_color}]{success_rate:.1f}%[/{rate_color}]")

                avg_time = total_time / processed_count
                summary_table.add_row("Average time per mod", f"[cyan]{avg_time:.1f}s[/cyan]")

            summary_table.add_row("Total time", f"[magenta]{str(timedelta(seconds=int(total_time)))}[/magenta]")

        console.print()
        console.print(summary_table)
        console.print()

        # Log final summary
        logger.info(f"Session completed: {success_count}/{processed_count} successful downloads in {total_time:.1f}s")

        return failed_mods
    
    except FileNotFoundError:
        print(f"Error: File '{json_file_path}' not found")
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format - {e}")
    except Exception as e:
        print(f"Error: {e}")


def save_progress(filename, current_mod, total_mods, processed, success, failed):
    """Save current progress to a file."""
    try:
        progress_data = {
            'timestamp': datetime.now().isoformat(),
            'current_mod': current_mod,
            'total_mods': total_mods,
            'processed': processed,
            'success': success,
            'failed': failed,
            'percentage': (current_mod / total_mods) * 100
        }
        with open(filename, 'w') as f:
            json.dump(progress_data, f, indent=2)
    except Exception as e:
        logger.warning(f"Could not save progress: {e}")


def create_enhanced_session():
    """Create a requests session with enhanced anti-bot measures."""
    session = requests.Session()

    # Configure retry strategy
    retry_strategy = Retry(
        total=CONFIG['MAX_RETRIES'],
        backoff_factor=CONFIG['RETRY_BACKOFF'],
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS"]
    )

    # Mount adapter with retry strategy
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # Set default timeout
    session.timeout = CONFIG['REQUEST_TIMEOUT']

    return session





def fetch_download_url(session, mod_id, file_id, rate_limiter=None, browser_session=None, output_dir=None, game_info=None):
    """
    Fetch the download URL from NexusMods using authenticated session with consistent browser fingerprinting.

    Args:
        session (requests.Session): Authenticated requests session
        mod_id (int): NexusMods mod ID
        file_id (int): NexusMods file ID
        rate_limiter (RateLimiter): Rate limiter for request throttling
        browser_session (BrowserSession): Consistent browser session for headers
        output_dir (str): Directory for debug files
        game_info (dict): Game information for URL construction

    Returns:
        str: NXM download URL if found, None otherwise
    """
    if rate_limiter is None:
        rate_limiter = RateLimiter()
    if browser_session is None:
        browser_session = BrowserSession()
    if output_dir is None:
        output_dir = CONFIG['OUTPUT_DIR']
    if game_info is None:
        # Default to cyberpunk2077 for backward compatibility
        game_info = SUPPORTED_GAMES['cyberpunk2077']

    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            # Construct the download page URL using game info
            game_path = game_info.get('url_path', 'cyberpunk2077')
            base_url = f"https://www.nexusmods.com/{game_path}/mods/{mod_id}"
            params = {
                'tab': 'files',
                'file_id': file_id,
                'nmm': '1',
                'mtm_source': 'nexusmodsapp',
                'mtm_campaign': 'collections',
            }

            # Get consistent headers from browser session
            referer = f"https://www.nexusmods.com/{game_path}/mods/{mod_id}"
            headers = browser_session.get_headers(referer=referer)

            # Make the request with timeout and stream
            logger.debug(f"Fetching download page for mod {mod_id}, file {file_id} (attempt {attempt + 1})")
            response = session.get(base_url, params=params, headers=headers,
                                 timeout=CONFIG['REQUEST_TIMEOUT'], stream=True)

            # Check for rate limiting
            if response.status_code == 429:
                retry_after = int(response.headers.get('Retry-After', CONFIG['RATE_LIMIT_DELAY']))
                print(f"     Rate limited, waiting {retry_after} seconds...")
                logger.warning(f"Rate limited for mod {mod_id}, waiting {retry_after}s")
                time.sleep(retry_after)
                continue

            response.raise_for_status()

            print(f"     Successfully fetched page (status: {response.status_code})")
            print(f"     Content-Type: {response.headers.get('content-type', 'unknown')}")
            print(f"     Content-Encoding: {response.headers.get('content-encoding', 'none')}")

            # Log response details for debugging
            logger.debug(f"Response for mod {mod_id}: status={response.status_code}, "
                        f"content-type={response.headers.get('content-type')}, "
                        f"content-length={response.headers.get('content-length')}")

            # Enhanced content decompression with multiple fallbacks
            html_content = None
            content_encoding = response.headers.get('content-encoding', '').lower()

            try:
                if content_encoding == 'br':
                    # Brotli compression
                    compressed = response.content
                    html_content = brotli.decompress(compressed).decode('utf-8')
                    logger.debug(f"Successfully decompressed brotli content for mod {mod_id}")
                elif content_encoding == 'gzip':
                    # Gzip compression
                    html_content = gzip.decompress(response.content).decode('utf-8')
                    logger.debug(f"Successfully decompressed gzip content for mod {mod_id}")
                else:
                    # Try response.text first (handles most cases automatically)
                    html_content = response.text

                    # Fallback: if content looks binary, try manual decompression
                    if len(html_content) > 0 and ord(html_content[0]) < 32 and html_content[0] not in '\t\n\r':
                        logger.debug(f"Content appears binary for mod {mod_id}, trying manual decompression")
                        try:
                            html_content = brotli.decompress(response.content).decode('utf-8')
                        except:
                            try:
                                html_content = gzip.decompress(response.content).decode('utf-8')
                            except:
                                html_content = response.content.decode('utf-8', errors='replace')

                print(f"     HTML content length: {len(html_content)} characters")

                # Verify content looks like HTML
                if '<html' in html_content.lower() or '<!doctype' in html_content.lower():
                    print(f"     ✓ Content appears to be valid HTML")
                    logger.debug(f"Valid HTML content received for mod {mod_id}")
                else:
                    print(f"     ⚠ Content may not be valid HTML (first 100 chars): {html_content[:100]}")
                    logger.warning(f"Suspicious content for mod {mod_id}: {html_content[:100]}")

            except Exception as decode_error:
                logger.error(f"Content decoding failed for mod {mod_id}: {decode_error}")
                print(f"     Error decoding content: {decode_error}")
                # Final fallback
                try:
                    html_content = response.content.decode('utf-8', errors='replace')
                    print(f"     Using fallback decoding, length: {len(html_content)} characters")
                except Exception as final_error:
                    logger.error(f"Complete decoding failure for mod {mod_id}: {final_error}")
                    print(f"     Complete failure to decode content: {final_error}")
                    if attempt < max_attempts - 1:
                        print(f"     Retrying in {2 ** attempt} seconds...")
                        time.sleep(2 ** attempt)
                        continue
                    return None

            # Parse the HTML to extract the download URL
            download_url_result = parse_download_url_from_html(html_content, mod_id, file_id, output_dir)
            if download_url_result:
                logger.info(f"Successfully extracted download URL for mod {mod_id}")
                return download_url_result
            elif attempt < max_attempts - 1:
                print(f"     No download URL found, retrying in {2 ** attempt} seconds...")
                logger.warning(f"No download URL found for mod {mod_id}, attempt {attempt + 1}")
                time.sleep(2 ** attempt)
                continue
            else:
                logger.error(f"Failed to extract download URL for mod {mod_id} after {max_attempts} attempts")
                return None

        except requests.exceptions.Timeout:
            logger.warning(f"Timeout for mod {mod_id}, attempt {attempt + 1}")
            print(f"     Request timeout (attempt {attempt + 1})")
            if attempt < max_attempts - 1:
                time.sleep(2 ** attempt)
                continue
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error for mod {mod_id}, attempt {attempt + 1}: {e}")
            print(f"     Request error: {e}")
            if attempt < max_attempts - 1:
                time.sleep(2 ** attempt)
                continue
        except Exception as e:
            logger.error(f"Unexpected error for mod {mod_id}, attempt {attempt + 1}: {e}")
            print(f"     Unexpected error: {e}")
            if attempt < max_attempts - 1:
                time.sleep(2 ** attempt)
                continue

    logger.error(f"All attempts failed for mod {mod_id}")
    return None


def parse_download_url_from_html(html_content, mod_id, file_id, output_dir=None):
    """
    Parse the download URL from the HTML content by looking for the slowDownloadButton.

    Args:
        html_content (str): HTML content of the download page
        mod_id (int): Mod ID for reference
        file_id (int): File ID for reference

    Returns:
        str: NXM download URL if found, None otherwise
    """
    try:
        # Check if we have valid HTML content
        if not html_content or len(html_content.strip()) < 100:
            print(f"     ✗ HTML content is too short or empty (length: {len(html_content) if html_content else 0})")
            return None

        soup = BeautifulSoup(html_content, 'html.parser')

        # Look for the slowDownloadButton with data-download-url attribute
        slow_download_button = soup.find('button', id='slowDownloadButton')
        if slow_download_button:
            print(f"     ✓ Found slowDownloadButton element")
            if slow_download_button.get('data-download-url'):
                download_url = slow_download_button['data-download-url']
                print(f"     ✓ Found download URL in slowDownloadButton")
                return download_url
            else:
                print(f"     ✗ slowDownloadButton found but no data-download-url attribute")
                print(f"     Button attributes: {slow_download_button.attrs}")
        else:
            print(f"     ✗ slowDownloadButton not found")

        # Alternative: Look for any element with data-download-url attribute
        download_elements = soup.find_all(attrs={'data-download-url': True})
        print(f"     Found {len(download_elements)} elements with data-download-url attribute")
        for element in download_elements:
            download_url = element['data-download-url']
            print(f"     ✓ Found download URL in element: {element.name} (id: {element.get('id', 'none')})")
            return download_url

        # Alternative: Look for NXM links in any attributes or text
        # Search for nxm:// URLs in the entire HTML
        nxm_pattern = r'nxm://[^"\'\s<>]+'
        nxm_matches = re.findall(nxm_pattern, html_content)
        print(f"     Found {len(nxm_matches)} NXM URLs in HTML content")

        if nxm_matches:
            # Filter for cyberpunk2077 and matching mod/file IDs if possible
            for nxm_url in nxm_matches:
                if 'cyberpunk2077' in nxm_url and str(mod_id) in nxm_url and str(file_id) in nxm_url:
                    print(f"     ✓ Found matching NXM URL in HTML content: {nxm_url[:100]}...")
                    return nxm_url
            # If no exact match, return the first cyberpunk2077 NXM URL
            for nxm_url in nxm_matches:
                if 'cyberpunk2077' in nxm_url:
                    print(f"     ✓ Found Cyberpunk 2077 NXM URL in HTML content: {nxm_url[:100]}...")
                    return nxm_url
            # If no cyberpunk2077 URLs, show what we found
            print(f"     Found NXM URLs but none for cyberpunk2077:")
            for i, url in enumerate(nxm_matches[:3]):  # Show first 3
                print(f"       {i+1}. {url[:100]}...")

        # Look for download buttons or links that might contain the URL
        download_buttons = soup.find_all(['button', 'a'], class_=re.compile(r'download', re.I))
        print(f"     Found {len(download_buttons)} potential download buttons/links")
        for button in download_buttons[:5]:  # Check first 5
            for attr in ['data-download-url', 'href', 'data-url', 'data-link']:
                if button.get(attr) and 'nxm://' in str(button.get(attr)):
                    print(f"     ✓ Found NXM URL in {button.name} {attr}: {button[attr][:100]}...")
                    return button[attr]

        print(f"     ✗ No download URL found for mod {mod_id}, file {file_id}")

        # For debugging, save the HTML and analysis to inspect manually
        if output_dir is None:
            output_dir = CONFIG['OUTPUT_DIR']
        save_debug_files(html_content, mod_id, file_id, soup, output_dir)

        return None

    except Exception as e:
        print(f"     ✗ Error parsing HTML: {e}")
        return None


def save_debug_files(html_content, mod_id, file_id, soup=None, output_dir=None):
    """Save debug files for manual inspection in organized directory."""
    if output_dir is None:
        output_dir = CONFIG['OUTPUT_DIR']

    # Create debug subdirectory
    debug_dir = os.path.join(output_dir, 'debug')
    if not os.path.exists(debug_dir):
        os.makedirs(debug_dir)

    base_filename = os.path.join(debug_dir, f'debug_mod_{mod_id}_file_{file_id}')

    # Save the raw HTML
    html_filename = f'{base_filename}.html'
    try:
        # Ensure we can write the content as readable text
        if isinstance(html_content, bytes):
            try:
                html_text = html_content.decode('utf-8')
            except UnicodeDecodeError:
                html_text = html_content.decode('utf-8', errors='replace')
        else:
            html_text = html_content

        # Double-check if content is still binary-looking
        if len(html_text) > 0 and ord(html_text[0]) < 32 and html_text[0] not in '\t\n\r':
            print(f"     Debug: Content still appears binary, attempting decompression...")
            import gzip
            import io
            try:
                # If html_content is a string but looks binary, it might be incorrectly decoded
                if isinstance(html_content, str):
                    # Convert back to bytes and try decompression
                    binary_content = html_content.encode('latin1')  # Preserve byte values
                else:
                    binary_content = html_content

                html_text = gzip.decompress(binary_content).decode('utf-8')
                print(f"     Debug: Successfully decompressed content for saving")
            except Exception as decomp_error:
                print(f"     Debug: Decompression failed: {decomp_error}")
                # Use the original text with replacements for unreadable chars
                html_text = ''.join(c if ord(c) >= 32 or c in '\t\n\r' else f'[BYTE:{ord(c)}]' for c in html_text[:10000])
                html_text += "\n\n<!-- Content was binary/compressed and may not be fully readable -->"

        with open(html_filename, 'w', encoding='utf-8') as f:
            f.write(html_text)
        print(f"     Debug: HTML saved to {html_filename}")

        # Verify the saved file is readable
        with open(html_filename, 'r', encoding='utf-8') as f:
            test_content = f.read(100)
            if '<html' in test_content.lower() or '<!doctype' in test_content.lower():
                print(f"     Debug: ✓ Saved HTML file appears readable")
            else:
                print(f"     Debug: ⚠ Saved HTML file may still have issues")

    except Exception as e:
        print(f"     Debug: Could not save HTML file: {e}")
        # Try saving as binary with a note
        try:
            with open(f'{base_filename}.bin', 'wb') as f:
                if isinstance(html_content, str):
                    f.write(html_content.encode('utf-8', errors='replace'))
                else:
                    f.write(html_content)

            # Also create a note file
            with open(f'{base_filename}_note.txt', 'w', encoding='utf-8') as f:
                f.write("The HTML content was saved as binary because it could not be properly decoded.\n")
                f.write("This usually means the content is compressed or in an unexpected format.\n")
                f.write(f"File size: {len(html_content)} bytes\n")
                f.write(f"Content type: {type(html_content)}\n")

            print(f"     Debug: Binary content saved to {base_filename}.bin with note")
        except Exception as e2:
            print(f"     Debug: Could not save binary file either: {e2}")

    # Save analysis file
    analysis_filename = f'{base_filename}_analysis.txt'
    try:
        with open(analysis_filename, 'w', encoding='utf-8') as f:
            f.write(f"Debug Analysis for Mod {mod_id}, File {file_id}\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"HTML Content Length: {len(html_content) if html_content else 0}\n")
            f.write(f"Content Type: {type(html_content)}\n\n")

            if soup:
                # Find all buttons
                buttons = soup.find_all('button')
                f.write(f"Total buttons found: {len(buttons)}\n")
                for i, button in enumerate(buttons[:10]):  # First 10 buttons
                    f.write(f"  Button {i+1}: id='{button.get('id', 'none')}', class='{button.get('class', 'none')}'\n")
                    if button.get('data-download-url'):
                        f.write(f"    data-download-url: {button['data-download-url']}\n")
                f.write("\n")

                # Find all elements with data-download-url
                download_elements = soup.find_all(attrs={'data-download-url': True})
                f.write(f"Elements with data-download-url: {len(download_elements)}\n")
                for elem in download_elements:
                    f.write(f"  {elem.name}: {elem.get('data-download-url')}\n")
                f.write("\n")

                # Search for NXM URLs
                nxm_pattern = r'nxm://[^"\'\s<>]+'
                nxm_matches = re.findall(nxm_pattern, str(soup))
                f.write(f"NXM URLs found: {len(nxm_matches)}\n")
                for url in nxm_matches:
                    f.write(f"  {url}\n")

        print(f"     Debug: Analysis saved to {analysis_filename}")
    except Exception as e:
        print(f"     Debug: Could not save analysis file: {e}")


def get_cookies_from_user(browser_session=None, output_dir=None):
    """
    Get cookies from user input and create an authenticated session with consistent browser headers.

    Args:
        browser_session (BrowserSession): Consistent browser session for headers
        output_dir (str): Directory for saving cookies file

    Returns:
        requests.Session: Authenticated session with cookies, or None if failed
    """
    if output_dir is None:
        output_dir = CONFIG['OUTPUT_DIR']
    if browser_session is None:
        browser_session = BrowserSession()

    cookies_file = os.path.join(output_dir, "nexusmods_cookies.json")

    # Try to load existing cookies first
    session = load_cookies_from_file(cookies_file, browser_session)
    if session and test_authentication(session, browser_session):
        print("✓ Using saved authentication cookies")
        logger.info("Successfully loaded saved authentication cookies")
        return enhance_session_security(session)

    print("=" * 60)
    print("NEXUSMODS COOKIE AUTHENTICATION")
    print("=" * 60)
    print("To avoid bot detection, please provide your NexusMods cookies manually.")
    print()
    print("How to get your cookies:")
    print("1. Open your browser and go to https://www.nexusmods.com")
    print("2. Log in to your account")
    print("3. Open Developer Tools (F12)")
    print("4. Go to Application/Storage tab > Cookies > https://www.nexusmods.com")
    print("5. Find and copy the following cookie values:")
    print()

    # Get essential cookies from user
    cookies = {}

    # Get session cookie (most important)
    while True:
        session_cookie = input("Enter your 'nexusmods_session' cookie value: ").strip()
        if session_cookie:
            cookies['nexusmods_session'] = session_cookie
            break
        print("Session cookie is required. Please try again.")

    # Get CSRF token (often needed)
    csrf_token = input("Enter your 'csrf_token' cookie value (or press Enter to skip): ").strip()
    if csrf_token:
        cookies['csrf_token'] = csrf_token

    # Get any additional cookies
    print("\nOptional: Enter any additional cookies (format: name=value, press Enter when done):")
    while True:
        additional_cookie = input("Additional cookie (or Enter to finish): ").strip()
        if not additional_cookie:
            break
        if '=' in additional_cookie:
            name, value = additional_cookie.split('=', 1)
            cookies[name.strip()] = value.strip()
            print(f"Added cookie: {name.strip()}")
        else:
            print("Invalid format. Use: name=value")

    # Create enhanced session with cookies and consistent browser headers
    session = create_session_from_cookies_dict(cookies, browser_session)
    session = enhance_session_security(session)

    # Test authentication with retry
    auth_attempts = 3
    for attempt in range(auth_attempts):
        if test_authentication(session, browser_session):
            print("✓ Authentication successful!")
            logger.info("Cookie authentication successful")
            save_cookies_to_file(cookies, cookies_file)
            print("✓ Cookies saved for future use")
            return session
        elif attempt < auth_attempts - 1:
            print(f"Authentication failed, retrying... (attempt {attempt + 2}/{auth_attempts})")
            time.sleep(2)

    console.print("[red]✗ Authentication failed after multiple attempts. Please check your cookies and try again.[/red]")
    logger.error("Cookie authentication failed after multiple attempts")
    return None


def enhance_session_security(session):
    """Enhance session with additional security measures."""
    # Configure session with enhanced settings
    session.headers.update({
        'DNT': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
    })

    # Configure retry strategy
    retry_strategy = Retry(
        total=CONFIG['MAX_RETRIES'],
        backoff_factor=CONFIG['RETRY_BACKOFF'],
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS"]
    )

    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session


def save_cookies_to_file(cookies_dict, filename):
    """Save cookies dictionary to a JSON file."""
    try:
        with open(filename, 'w') as f:
            json.dump(cookies_dict, f, indent=2)
    except Exception as e:
        print(f"Warning: Could not save cookies: {e}")


def load_cookies_from_file(filename, browser_session=None):
    """Load cookies from JSON file and create a session with consistent headers."""
    try:
        with open(filename, 'r') as f:
            cookies_dict = json.load(f)
        return create_session_from_cookies_dict(cookies_dict, browser_session)
    except (FileNotFoundError, json.JSONDecodeError):
        return None


def create_session_from_cookies_dict(cookies_dict, browser_session=None):
    """Create a requests session with the given cookies dictionary and consistent browser headers."""
    session = create_enhanced_session()

    # Set cookies with proper domain and security settings
    for name, value in cookies_dict.items():
        session.cookies.set(
            name=name,
            value=value,
            domain='.nexusmods.com',
            secure=True,
            rest={'HttpOnly': True}
        )

    # Add consistent browser headers if provided
    if browser_session:
        session.headers.update(browser_session.get_headers())

    return session


def test_authentication(session, browser_session=None):
    """Test if the session is properly authenticated with consistent browser headers."""
    try:
        # Test multiple endpoints to ensure authentication is working
        test_urls = [
            "https://www.nexusmods.com/users/myaccount",
            "https://www.nexusmods.com/cyberpunk2077/users/myaccount"
        ]

        for url in test_urls:
            try:
                # Use consistent headers if browser session is provided
                headers = browser_session.get_headers() if browser_session else {}
                response = session.get(url, timeout=15, headers=headers)

                if response.status_code == 200:
                    # Check for authentication indicators
                    content = response.text.lower()
                    auth_indicators = ['myaccount', 'logout', 'profile', 'premium']

                    if any(indicator in content for indicator in auth_indicators):
                        logger.info(f"Authentication verified via {url}")
                        return True

            except requests.RequestException as e:
                logger.debug(f"Auth test failed for {url}: {e}")
                continue

        logger.warning("Authentication test failed for all endpoints")
        return False

    except Exception as e:
        logger.error(f"Authentication test error: {e}")
        return False


def get_user_inputs(game_info=None):
    """Get user inputs for configuration with enhanced options."""
    if game_info:
        game_icon = game_info.get('icon', '🎮')
        game_name = game_info.get('name', 'Unknown Game')
        game_color = game_info.get('color', 'white')

        console.print(Panel(
            f"{game_icon} [bold {game_color}]{game_name}[/bold {game_color}] Collection Downloader",
            title="🚀 Configuration",
            border_style=game_color
        ))
    else:
        console.print(Panel(
            "🎮 [bold white]NexusMods Collection Downloader[/bold white]",
            title="🚀 Configuration",
            border_style="blue"
        ))

    # Get starting position
    while True:
        try:
            start_input = input("Start from which mod? (Enter number, or press Enter for 1): ").strip()
            if not start_input:
                start_from = 1
                break
            start_from = int(start_input)
            if start_from < 1:
                print("Starting position must be 1 or greater.")
                continue
            break
        except ValueError:
            print("Please enter a valid number.")

    # Get delay with intelligent defaults
    while True:
        try:
            delay_input = input("Delay between downloads in seconds? (Enter number, or press Enter for smart delay): ").strip()
            if not delay_input:
                delay = random.randint(CONFIG['MIN_DELAY'], CONFIG['MAX_DELAY'])
                print(f"Using smart delay: {delay} seconds (randomized between {CONFIG['MIN_DELAY']}-{CONFIG['MAX_DELAY']}s)")
                break
            delay = int(delay_input)
            if delay < 0:
                print("Delay must be 0 or greater.")
                continue
            break
        except ValueError:
            print("Please enter a valid number.")

    # Preview mode
    preview_input = input("Preview mode only? (y/N): ").strip().lower()
    preview_only = preview_input in ['y', 'yes']

    # Advanced options
    print("\nAdvanced Options:")

    # Verbose logging
    verbose_input = input("Enable verbose logging? (y/N): ").strip().lower()
    verbose = verbose_input in ['y', 'yes']
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        print("✓ Verbose logging enabled")

    # Retry failed downloads
    retry_input = input("Retry failed downloads at the end? (Y/n): ").strip().lower()
    retry_failed = retry_input not in ['n', 'no']

    # Save failed mods list
    save_failed_input = input("Save list of failed mods to file? (Y/n): ").strip().lower()
    save_failed = save_failed_input not in ['n', 'no']

    return {
        'start_from': start_from,
        'delay': delay,
        'preview_only': preview_only,
        'verbose': verbose,
        'retry_failed': retry_failed,
        'save_failed': save_failed
    }


def main():
    """Enhanced main function with comprehensive error handling and consistent browser simulation."""
    global logger
    try:
        # Setup output directory and logging
        output_dir = setup_output_directory()
        logger = setup_logging(output_dir)
        logger.info("Starting NexusMods Collection Downloader - Enhanced Edition")

        # Initialize consistent browser session
        browser_session = BrowserSession()
        logger.info(f"Initialized browser session: {browser_session.browser_type}")

        # Default file path
        json_file_path = "collection.json"

        # Allow command line argument for file path
        if len(sys.argv) > 1:
            json_file_path = sys.argv[1]

        # Check if file exists
        if not os.path.exists(json_file_path):
            print(f"Error: File '{json_file_path}' not found")
            print("Please make sure the collection.json file is in the same directory as this script,")
            print("or provide the correct path as a command line argument.")
            logger.error(f"Collection file not found: {json_file_path}")
            return

        # Validate JSON file and detect game
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                collection_data = json.load(f)
            if 'mods' not in collection_data:
                console.print("[red]Error: Invalid collection file - 'mods' key not found[/red]")
                logger.error("Invalid collection file format")
                return

            # Detect game from collection
            game_info = detect_game_from_collection(json_file_path)
            if not game_info:
                console.print("[red]✗ Unsupported or undetected game. Cannot proceed.[/red]")
                return

            game_icon = game_info.get('icon', '🎮')
            game_name = game_info.get('name', 'Unknown Game')
            game_color = game_info.get('color', 'white')

            console.print(f"[green]✓ Collection file validated[/green]")
            console.print(f"[green]✓ Game detected:[/green] {game_icon} [bold {game_color}]{game_name}[/bold {game_color}]")
            console.print(f"[green]✓ Found {len(collection_data['mods'])} mods[/green]")

        except json.JSONDecodeError as e:
            console.print(f"[red]Error: Invalid JSON format in collection file: {e}[/red]")
            logger.error(f"JSON decode error: {e}")
            return

        # Authenticate with NexusMods using cookies
        console.print(f"\n[bold blue]Step 1: Authentication[/bold blue]")
        session = get_cookies_from_user(browser_session, output_dir)
        if not session:
            console.print("[red]✗ Authentication failed. Cannot proceed.[/red]")
            logger.error("Authentication failed")
            return

        # Get user inputs
        console.print(f"\n[bold blue]Step 2: Configuration[/bold blue]")
        config = get_user_inputs(game_info)

        # Create rate limiter
        rate_limiter = RateLimiter(CONFIG['MIN_DELAY'], CONFIG['MAX_DELAY'])

        console.print(f"\n[bold blue]Step 3: Processing[/bold blue]")
        console.print(f"Reading mod information from: [bold cyan]{json_file_path}[/bold cyan]")

        # Process the mods
        failed_mods = extract_mod_info(
            json_file_path,
            session,
            config['start_from'],
            config['delay'],
            config['preview_only'],
            rate_limiter,
            browser_session,
            output_dir,
            game_info
        )

        # Handle failed mods if requested
        if config['retry_failed'] and failed_mods and not config['preview_only']:
            print(f"\nRetrying {len(failed_mods)} failed mods...")
            logger.info(f"Retrying {len(failed_mods)} failed mods")
            # Implement retry logic here if needed

        # Save failed mods list if requested
        if config['save_failed'] and failed_mods:
            failed_file = os.path.join(output_dir, f"failed_mods_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            try:
                with open(failed_file, 'w') as f:
                    json.dump(failed_mods, f, indent=2)
                console.print(f"[green]✓ Failed mods list saved to: {failed_file}[/green]")
                logger.info(f"Failed mods list saved to {failed_file}")
            except Exception as e:
                console.print(f"[yellow]⚠ Could not save failed mods list: {e}[/yellow]")
                logger.warning(f"Could not save failed mods list: {e}")

        # Final completion message
        if game_info:
            game_icon = game_info.get('icon', '🎮')
            game_name = game_info.get('name', 'Unknown Game')
            game_color = game_info.get('color', 'white')
            console.print(f"\n🎉 [bold green]{game_icon} {game_name} collection processing completed successfully![/bold green]")
        else:
            console.print(f"\n🎉 [bold green]Process completed successfully![/bold green]")
        logger.info("Process completed successfully")

    except KeyboardInterrupt:
        print("\n⚠ Process interrupted by user")
        logger.info("Process interrupted by user")
    except Exception as e:
        print(f"\n✗ Unexpected error: {e}")
        logger.error(f"Unexpected error in main: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    main()
